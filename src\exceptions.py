"""
全局异常定义
"""
from typing import Any, Dict, Optional

from fastapi import HTTPException, status


class BaseCustomException(HTTPException):
    """自定义异常基类"""
    
    def __init__(
        self,
        status_code: int,
        detail: str,
        error_code: str,
        headers: Optional[Dict[str, Any]] = None,
    ):
        super().__init__(status_code=status_code, detail=detail, headers=headers)
        self.error_code = error_code


class ValidationException(BaseCustomException):
    """验证异常"""
    
    def __init__(self, detail: str = "Validation error"):
        super().__init__(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=detail,
            error_code="VALIDATION_ERROR"
        )


class AuthenticationException(BaseCustomException):
    """认证异常"""
    
    def __init__(self, detail: str = "Authentication failed"):
        super().__init__(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=detail,
            error_code="AUTHENTICATION_FAILED",
            headers={"WWW-Authenticate": "Bearer"}
        )


class AuthorizationException(BaseCustomException):
    """授权异常"""
    
    def __init__(self, detail: str = "Insufficient permissions"):
        super().__init__(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=detail,
            error_code="INSUFFICIENT_PERMISSIONS"
        )


class NotFoundException(BaseCustomException):
    """资源未找到异常"""
    
    def __init__(self, detail: str = "Resource not found"):
        super().__init__(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=detail,
            error_code="NOT_FOUND"
        )


class ConflictException(BaseCustomException):
    """冲突异常"""
    
    def __init__(self, detail: str = "Resource conflict"):
        super().__init__(
            status_code=status.HTTP_409_CONFLICT,
            detail=detail,
            error_code="CONFLICT"
        )


class InternalServerException(BaseCustomException):
    """内部服务器异常"""
    
    def __init__(self, detail: str = "Internal server error"):
        super().__init__(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=detail,
            error_code="INTERNAL_ERROR"
        )


# 特定业务异常
class UserNotFoundException(NotFoundException):
    """用户未找到异常"""
    
    def __init__(self, user_id: Optional[str] = None):
        detail = f"User {user_id} not found" if user_id else "User not found"
        super().__init__(detail=detail)
        self.error_code = "USER_NOT_FOUND"


class UserAlreadyExistsException(ConflictException):
    """用户已存在异常"""
    
    def __init__(self, email: Optional[str] = None):
        detail = f"User with email {email} already exists" if email else "User already exists"
        super().__init__(detail=detail)
        self.error_code = "USER_ALREADY_EXISTS"


class InvalidCredentialsException(AuthenticationException):
    """无效凭据异常"""
    
    def __init__(self):
        super().__init__(detail="Invalid email or password")
        self.error_code = "INVALID_CREDENTIALS"


class TokenExpiredException(AuthenticationException):
    """令牌过期异常"""
    
    def __init__(self):
        super().__init__(detail="Token has expired")
        self.error_code = "TOKEN_EXPIRED"


class StockNotFoundException(NotFoundException):
    """股票未找到异常"""
    
    def __init__(self, symbol: Optional[str] = None):
        detail = f"Stock {symbol} not found" if symbol else "Stock not found"
        super().__init__(detail=detail)
        self.error_code = "STOCK_NOT_FOUND"


class DataSourceException(InternalServerException):
    """数据源异常"""
    
    def __init__(self, detail: str = "Data source error"):
        super().__init__(detail=detail)
        self.error_code = "DATA_SOURCE_ERROR"


class AnalysisFailedException(InternalServerException):
    """分析失败异常"""
    
    def __init__(self, detail: str = "Analysis failed"):
        super().__init__(detail=detail)
        self.error_code = "ANALYSIS_FAILED"

"""
股票数据模块
"""
from src.stocks.dependencies import (
    get_price_query_params,
    get_stock_by_id,
    get_stock_by_symbol,
    get_stock_query_params,
)
from src.stocks.models import Stock, StockDividend, StockPrice
from src.stocks.router import router
from src.stocks.schemas import (
    PriceQuery,
    StockCreate,
    StockDividendCreate,
    StockDividendResponse,
    StockPriceCreate,
    StockPriceResponse,
    StockQuery,
    StockResponse,
    StockUpdate,
)
from src.stocks.service import StockService, get_stock_service

__all__ = [
    # Models
    "Stock",
    "StockPrice",
    "StockDividend",
    # Schemas
    "StockCreate",
    "StockUpdate",
    "StockResponse",
    "StockQuery",
    "StockPriceCreate",
    "StockPriceResponse",
    "StockDividendCreate",
    "StockDividendResponse",
    "PriceQuery",
    # Service
    "StockService",
    "get_stock_service",
    # Dependencies
    "get_stock_by_symbol",
    "get_stock_by_id",
    "get_stock_query_params",
    "get_price_query_params",
    # Router
    "router",
]

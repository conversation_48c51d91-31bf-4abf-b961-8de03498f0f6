"""
股票数据相关的依赖项
"""
from uuid import UUID

from fastapi import Depends, Query

from src.constants import StockMarket, TimeFrame
from src.exceptions import StockNotFoundException
from src.stocks.models import Stock
from src.stocks.schemas import PriceQuery, StockQuery
from src.stocks.service import StockService, get_stock_service


async def get_stock_by_symbol(
    symbol: str,
    stock_service: StockService = Depends(get_stock_service),
) -> Stock:
    """根据股票代码获取股票信息的依赖项"""
    stock = await stock_service.get_stock_by_symbol(symbol)
    if not stock:
        raise StockNotFoundException(symbol)
    return stock


async def get_stock_by_id(
    stock_id: UUID,
    stock_service: StockService = Depends(get_stock_service),
) -> Stock:
    """根据股票ID获取股票信息的依赖项"""
    stock = await stock_service.get_stock_by_id(stock_id)
    if not stock:
        raise StockNotFoundException()
    return stock


def get_stock_query_params(
    symbol: str = Query(None, description="股票代码"),
    name: str = Query(None, description="股票名称"),
    market: StockMarket = Query(None, description="交易市场"),
    industry: str = Query(None, description="所属行业"),
    sector: str = Query(None, description="所属板块"),
    is_active: bool = Query(None, description="是否活跃交易"),
) -> StockQuery:
    """获取股票查询参数的依赖项"""
    return StockQuery(
        symbol=symbol,
        name=name,
        market=market,
        industry=industry,
        sector=sector,
        is_active=is_active,
    )


def get_price_query_params(
    symbol: str = Query(description="股票代码"),
    start_date: str = Query(None, description="开始日期 (YYYY-MM-DD)"),
    end_date: str = Query(None, description="结束日期 (YYYY-MM-DD)"),
    time_frame: TimeFrame = Query(TimeFrame.DAY_1, description="时间周期"),
) -> PriceQuery:
    """获取价格查询参数的依赖项"""
    from datetime import datetime
    
    start_date_obj = None
    end_date_obj = None
    
    if start_date:
        try:
            start_date_obj = datetime.strptime(start_date, "%Y-%m-%d").date()
        except ValueError:
            raise ValueError("Invalid start_date format. Use YYYY-MM-DD")
    
    if end_date:
        try:
            end_date_obj = datetime.strptime(end_date, "%Y-%m-%d").date()
        except ValueError:
            raise ValueError("Invalid end_date format. Use YYYY-MM-DD")
    
    return PriceQuery(
        symbol=symbol,
        start_date=start_date_obj,
        end_date=end_date_obj,
        time_frame=time_frame,
    )

"""
用户认证相关的依赖项
"""
from typing import Optional
from uuid import UUID

from fastapi import Depends, HTTPException, status
from fastapi.security import HTT<PERSON>uthorizationCredentials, HTTPBearer
from sqlalchemy.ext.asyncio import AsyncSession

from src.auth.models import User
from src.auth.service import AuthService, get_auth_service
from src.constants import User<PERSON>ole
from src.database import get_db
from src.exceptions import AuthenticationException, AuthorizationException

# HTTP Bearer 认证方案
security = HTTPBearer()


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    auth_service: AuthService = Depends(get_auth_service),
) -> User:
    """获取当前用户"""
    try:
        # 验证令牌
        token_data = auth_service.verify_token(credentials.credentials)
        
        # 获取用户信息
        user = await auth_service.get_user_by_id(token_data.user_id)
        if user is None:
            raise AuthenticationException("User not found")
        
        if not user.is_active:
            raise AuthenticationException("User account is inactive")
        
        return user
    
    except Exception as e:
        if isinstance(e, (AuthenticationException, AuthorizationException)):
            raise e
        raise AuthenticationException("Could not validate credentials")


async def get_current_active_user(
    current_user: User = Depends(get_current_user),
) -> User:
    """获取当前活跃用户"""
    if not current_user.is_active:
        raise AuthenticationException("User account is inactive")
    return current_user


async def get_current_verified_user(
    current_user: User = Depends(get_current_active_user),
) -> User:
    """获取当前已验证用户"""
    if not current_user.is_verified:
        raise AuthenticationException("Email not verified")
    return current_user


def require_role(required_role: UserRole):
    """要求特定角色的依赖项工厂"""
    
    async def role_checker(
        current_user: User = Depends(get_current_active_user),
    ) -> User:
        """检查用户角色"""
        if current_user.role != required_role:
            raise AuthorizationException(
                f"Operation requires {required_role.value} role"
            )
        return current_user
    
    return role_checker


def require_roles(required_roles: list[UserRole]):
    """要求多个角色之一的依赖项工厂"""
    
    async def roles_checker(
        current_user: User = Depends(get_current_active_user),
    ) -> User:
        """检查用户角色"""
        if current_user.role not in required_roles:
            roles_str = ", ".join([role.value for role in required_roles])
            raise AuthorizationException(
                f"Operation requires one of the following roles: {roles_str}"
            )
        return current_user
    
    return roles_checker


# 常用角色依赖项
require_admin = require_role(UserRole.ADMIN)
require_premium = require_roles([UserRole.PREMIUM, UserRole.ADMIN])


async def get_optional_current_user(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(
        HTTPBearer(auto_error=False)
    ),
    auth_service: AuthService = Depends(get_auth_service),
) -> Optional[User]:
    """获取可选的当前用户（用于可选认证的端点）"""
    if credentials is None:
        return None
    
    try:
        # 验证令牌
        token_data = auth_service.verify_token(credentials.credentials)
        
        # 获取用户信息
        user = await auth_service.get_user_by_id(token_data.user_id)
        if user is None or not user.is_active:
            return None
        
        return user
    
    except Exception:
        return None


async def validate_user_access(
    user_id: UUID,
    current_user: User = Depends(get_current_active_user),
) -> User:
    """验证用户访问权限（只能访问自己的资源或管理员可以访问所有）"""
    if current_user.role == UserRole.ADMIN or current_user.id == user_id:
        return current_user
    
    raise AuthorizationException("Access denied")


class UserAccessChecker:
    """用户访问检查器"""
    
    def __init__(self, user_id: UUID):
        self.user_id = user_id
    
    async def __call__(
        self,
        current_user: User = Depends(get_current_active_user),
    ) -> User:
        """检查用户访问权限"""
        return await validate_user_access(self.user_id, current_user)

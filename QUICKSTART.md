# 快速启动指南

## 方式一：使用开发脚本（推荐）

1. **安装依赖**
   ```bash
   pip install -r requirements/dev.txt
   ```

2. **配置环境变量**
   ```bash
   cp .env.example .env
   # 编辑 .env 文件，配置数据库连接等信息
   ```

3. **运行开发脚本**
   ```bash
   python scripts/start_dev.py
   ```
   
   这个脚本会自动：
   - 检查依赖
   - 检查环境变量
   - 运行数据库迁移
   - 初始化演示数据（可选）
   - 启动开发服务器

## 方式二：手动启动

1. **安装依赖**
   ```bash
   pip install -r requirements/dev.txt
   ```

2. **配置环境变量**
   ```bash
   cp .env.example .env
   ```

3. **初始化数据库**
   ```bash
   # 创建迁移
   alembic revision --autogenerate -m "Initial migration"
   
   # 执行迁移
   alembic upgrade head
   ```

4. **初始化演示数据（可选）**
   ```bash
   python scripts/init_demo_data.py
   ```

5. **启动服务器**
   ```bash
   python run.py
   # 或
   uvicorn src.main:app --reload --host 0.0.0.0 --port 8000
   ```

## 方式三：使用 Docker

1. **使用 Docker Compose**
   ```bash
   docker-compose up -d
   ```

2. **初始化数据库**
   ```bash
   docker-compose exec app alembic upgrade head
   ```

3. **初始化演示数据**
   ```bash
   docker-compose exec app python scripts/init_demo_data.py
   ```

## 访问应用

启动成功后，可以访问：

- **API 文档**: http://localhost:8000/docs
- **ReDoc 文档**: http://localhost:8000/redoc
- **健康检查**: http://localhost:8000/health

## 演示账户

如果初始化了演示数据，可以使用以下账户登录：

- **管理员**: <EMAIL> / Admin123456
- **普通用户**: <EMAIL> / User123456
- **高级用户**: <EMAIL> / Premium123456

## 主要 API 端点

### 认证
- `POST /api/v1/auth/register` - 用户注册
- `POST /api/v1/auth/login` - 用户登录
- `GET /api/v1/auth/me` - 获取当前用户信息

### 股票数据
- `GET /api/v1/stocks/` - 获取股票列表
- `GET /api/v1/stocks/{symbol}` - 获取股票详情
- `GET /api/v1/stocks/{symbol}/prices` - 获取价格历史

### 分析功能
- `GET /api/v1/analysis/` - 获取分析列表
- `POST /api/v1/analysis/` - 创建分析
- `POST /api/v1/analysis/{id}/execute` - 执行分析

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查 PostgreSQL 是否运行
   - 确认 .env 文件中的 DATABASE_URL 配置正确

2. **依赖安装失败**
   - 确保使用 Python 3.11+
   - 尝试升级 pip: `pip install --upgrade pip`

3. **端口被占用**
   - 修改 .env 文件中的端口配置
   - 或杀死占用端口的进程

### 日志查看

- 应用日志会输出到控制台
- Docker 环境下使用: `docker-compose logs -f app`

## 开发建议

1. **代码格式化**
   ```bash
   black src/
   isort src/
   ```

2. **代码检查**
   ```bash
   ruff check src/
   mypy src/
   ```

3. **运行测试**
   ```bash
   pytest
   ```

4. **生成迁移**
   ```bash
   alembic revision --autogenerate -m "描述信息"
   alembic upgrade head
   ```

"""
股票数据服务
"""
from datetime import date, datetime
from typing import Optional, Sequence
from uuid import UUID

from fastapi import Depends
from sqlalchemy import and_, desc, func, or_, select
from sqlalchemy.ext.asyncio import AsyncSession

from src.database import get_db
from src.exceptions import NotFoundException, StockNotFoundException
from src.pagination import PaginationParams, paginate
from src.stocks.models import Stock, StockDividend, StockPrice
from src.stocks.schemas import (
    PriceQuery,
    StockCreate,
    StockDividendCreate,
    StockPriceCreate,
    StockQuery,
    StockUpdate,
)


class StockService:
    """股票数据服务类"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def get_stock_by_symbol(self, symbol: str) -> Optional[Stock]:
        """根据股票代码获取股票信息"""
        query = select(Stock).where(Stock.symbol == symbol.upper())
        result = await self.db.execute(query)
        return result.scalar_one_or_none()
    
    async def get_stock_by_id(self, stock_id: UUID) -> Optional[Stock]:
        """根据ID获取股票信息"""
        query = select(Stock).where(Stock.id == stock_id)
        result = await self.db.execute(query)
        return result.scalar_one_or_none()
    
    async def create_stock(self, stock_create: StockCreate) -> Stock:
        """创建股票"""
        # 检查股票代码是否已存在
        existing_stock = await self.get_stock_by_symbol(stock_create.symbol)
        if existing_stock:
            raise ValueError(f"Stock with symbol {stock_create.symbol} already exists")
        
        # 创建新股票
        db_stock = Stock(**stock_create.model_dump())
        self.db.add(db_stock)
        await self.db.commit()
        await self.db.refresh(db_stock)
        
        return db_stock
    
    async def update_stock(self, stock_id: UUID, stock_update: StockUpdate) -> Stock:
        """更新股票信息"""
        stock = await self.get_stock_by_id(stock_id)
        if not stock:
            raise StockNotFoundException()
        
        # 更新字段
        for field, value in stock_update.model_dump(exclude_unset=True).items():
            setattr(stock, field, value)
        
        await self.db.commit()
        await self.db.refresh(stock)
        
        return stock
    
    async def delete_stock(self, stock_id: UUID) -> bool:
        """删除股票"""
        stock = await self.get_stock_by_id(stock_id)
        if not stock:
            raise StockNotFoundException()
        
        await self.db.delete(stock)
        await self.db.commit()
        
        return True
    
    async def search_stocks(
        self,
        query_params: StockQuery,
        pagination: PaginationParams,
    ) -> tuple[Sequence[Stock], int]:
        """搜索股票"""
        query = select(Stock)
        
        # 构建查询条件
        conditions = []
        
        if query_params.symbol:
            conditions.append(Stock.symbol.ilike(f"%{query_params.symbol}%"))
        
        if query_params.name:
            conditions.append(Stock.name.ilike(f"%{query_params.name}%"))
        
        if query_params.market:
            conditions.append(Stock.market == query_params.market)
        
        if query_params.industry:
            conditions.append(Stock.industry.ilike(f"%{query_params.industry}%"))
        
        if query_params.sector:
            conditions.append(Stock.sector.ilike(f"%{query_params.sector}%"))
        
        if query_params.is_active is not None:
            conditions.append(Stock.is_active == query_params.is_active)
        
        if conditions:
            query = query.where(and_(*conditions))
        
        # 排序
        query = query.order_by(Stock.symbol)
        
        # 分页查询
        return await paginate(self.db, query, pagination)
    
    async def get_stock_price(
        self,
        symbol: str,
        trade_date: date,
    ) -> Optional[StockPrice]:
        """获取指定日期的股票价格"""
        query = select(StockPrice).where(
            and_(
                StockPrice.symbol == symbol.upper(),
                StockPrice.trade_date == trade_date,
            )
        )
        result = await self.db.execute(query)
        return result.scalar_one_or_none()
    
    async def get_stock_prices(
        self,
        price_query: PriceQuery,
        pagination: PaginationParams,
    ) -> tuple[Sequence[StockPrice], int]:
        """获取股票价格历史数据"""
        query = select(StockPrice).where(StockPrice.symbol == price_query.symbol.upper())
        
        # 日期范围过滤
        if price_query.start_date:
            query = query.where(StockPrice.trade_date >= price_query.start_date)
        
        if price_query.end_date:
            query = query.where(StockPrice.trade_date <= price_query.end_date)
        
        # 按日期降序排列
        query = query.order_by(desc(StockPrice.trade_date))
        
        # 分页查询
        return await paginate(self.db, query, pagination)
    
    async def create_stock_price(self, price_create: StockPriceCreate) -> StockPrice:
        """创建股票价格数据"""
        # 检查股票是否存在
        stock = await self.get_stock_by_symbol(price_create.symbol)
        if not stock:
            raise StockNotFoundException(price_create.symbol)
        
        # 检查是否已存在相同日期的数据
        existing_price = await self.get_stock_price(
            price_create.symbol,
            price_create.trade_date
        )
        if existing_price:
            raise ValueError(
                f"Price data for {price_create.symbol} on {price_create.trade_date} already exists"
            )
        
        # 创建价格数据
        db_price = StockPrice(**price_create.model_dump())
        self.db.add(db_price)
        await self.db.commit()
        await self.db.refresh(db_price)
        
        return db_price
    
    async def batch_create_stock_prices(
        self,
        prices: list[StockPriceCreate],
    ) -> list[StockPrice]:
        """批量创建股票价格数据"""
        db_prices = []
        
        for price_create in prices:
            # 检查股票是否存在
            stock = await self.get_stock_by_symbol(price_create.symbol)
            if not stock:
                continue  # 跳过不存在的股票
            
            # 检查是否已存在相同日期的数据
            existing_price = await self.get_stock_price(
                price_create.symbol,
                price_create.trade_date
            )
            if existing_price:
                continue  # 跳过已存在的数据
            
            db_price = StockPrice(**price_create.model_dump())
            db_prices.append(db_price)
        
        if db_prices:
            self.db.add_all(db_prices)
            await self.db.commit()
            
            # 刷新所有对象
            for db_price in db_prices:
                await self.db.refresh(db_price)
        
        return db_prices
    
    async def get_latest_price(self, symbol: str) -> Optional[StockPrice]:
        """获取股票最新价格"""
        query = (
            select(StockPrice)
            .where(StockPrice.symbol == symbol.upper())
            .order_by(desc(StockPrice.trade_date))
            .limit(1)
        )
        result = await self.db.execute(query)
        return result.scalar_one_or_none()
    
    async def get_stock_dividends(
        self,
        symbol: str,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
    ) -> Sequence[StockDividend]:
        """获取股票分红数据"""
        query = select(StockDividend).where(StockDividend.symbol == symbol.upper())
        
        if start_date:
            query = query.where(StockDividend.ex_date >= start_date)
        
        if end_date:
            query = query.where(StockDividend.ex_date <= end_date)
        
        query = query.order_by(desc(StockDividend.ex_date))
        
        result = await self.db.execute(query)
        return result.scalars().all()
    
    async def create_stock_dividend(
        self,
        dividend_create: StockDividendCreate,
    ) -> StockDividend:
        """创建股票分红数据"""
        # 检查股票是否存在
        stock = await self.get_stock_by_symbol(dividend_create.symbol)
        if not stock:
            raise StockNotFoundException(dividend_create.symbol)
        
        # 创建分红数据
        db_dividend = StockDividend(**dividend_create.model_dump())
        self.db.add(db_dividend)
        await self.db.commit()
        await self.db.refresh(db_dividend)
        
        return db_dividend


def get_stock_service(db: AsyncSession = Depends(get_db)) -> StockService:
    """获取股票服务实例"""
    return StockService(db)

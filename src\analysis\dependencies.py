"""
股票分析相关的依赖项
"""
from typing import Optional
from uuid import UUID

from fastapi import Depends, Query

from src.analysis.models import Analysis, AnalysisTemplate
from src.analysis.schemas import AnalysisQuery
from src.analysis.service import AnalysisService, get_analysis_service
from src.auth.dependencies import get_current_active_user
from src.auth.models import User
from src.constants import AnalysisType
from src.exceptions import AuthorizationException, NotFoundException


async def get_analysis_by_id(
    analysis_id: UUID,
    analysis_service: AnalysisService = Depends(get_analysis_service),
) -> Analysis:
    """根据分析ID获取分析的依赖项"""
    analysis = await analysis_service.get_analysis_by_id(analysis_id)
    if not analysis:
        raise NotFoundException("Analysis not found")
    return analysis


async def get_user_analysis(
    analysis_id: UUID,
    current_user: User = Depends(get_current_active_user),
    analysis_service: AnalysisService = Depends(get_analysis_service),
) -> Analysis:
    """获取用户自己的分析（或管理员可以访问所有分析）"""
    analysis = await analysis_service.get_analysis_by_id(analysis_id)
    if not analysis:
        raise NotFoundException("Analysis not found")
    
    # 检查权限：只有分析创建者或管理员可以访问
    if analysis.user_id != current_user.id and current_user.role != "admin":
        raise AuthorizationException("Access denied")
    
    return analysis


async def get_template_by_id(
    template_id: UUID,
    analysis_service: AnalysisService = Depends(get_analysis_service),
) -> AnalysisTemplate:
    """根据模板ID获取模板的依赖项"""
    template = await analysis_service.get_template_by_id(template_id)
    if not template:
        raise NotFoundException("Template not found")
    return template


async def get_user_template(
    template_id: UUID,
    current_user: User = Depends(get_current_active_user),
    analysis_service: AnalysisService = Depends(get_analysis_service),
) -> AnalysisTemplate:
    """获取用户自己的模板（或管理员可以访问所有模板）"""
    template = await analysis_service.get_template_by_id(template_id)
    if not template:
        raise NotFoundException("Template not found")
    
    # 检查权限：只有模板创建者或管理员可以访问私有模板
    if not template.is_public and template.created_by != current_user.id and current_user.role != "admin":
        raise AuthorizationException("Access denied")
    
    return template


def get_analysis_query_params(
    symbol: Optional[str] = Query(None, description="股票代码"),
    analysis_type: Optional[AnalysisType] = Query(None, description="分析类型"),
    status: Optional[str] = Query(None, description="分析状态"),
    user_id: Optional[UUID] = Query(None, description="用户ID"),
) -> AnalysisQuery:
    """获取分析查询参数的依赖项"""
    return AnalysisQuery(
        symbol=symbol,
        analysis_type=analysis_type,
        status=status,
        user_id=user_id,
    )


def get_template_query_params(
    analysis_type: Optional[AnalysisType] = Query(None, description="分析类型"),
    public_only: bool = Query(False, description="仅公开模板"),
) -> dict:
    """获取模板查询参数的依赖项"""
    return {
        "analysis_type": analysis_type,
        "public_only": public_only,
    }

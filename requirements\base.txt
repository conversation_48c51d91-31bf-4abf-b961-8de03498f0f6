# FastAPI 核心依赖
fastapi==0.104.1
uvicorn[standard]==0.24.0

# 数据库相关
sqlalchemy==2.0.23
asyncpg==0.29.0
alembic==1.12.1

# Pydantic 和设置
pydantic==2.5.0
pydantic-settings==2.1.0

# 认证和安全
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# Redis (可选)
redis==5.0.1

# HTTP 客户端
httpx==0.25.2

# 日期时间处理
python-dateutil==2.8.2

# 数据处理
pandas==2.1.4
numpy==1.25.2

# 股票数据源
yfinance==0.2.28
tushare==1.2.89
akshare==1.12.82

# 技术分析
talib-binary==0.4.26
ta==0.10.2

# 图表和可视化
plotly==5.17.0
matplotlib==3.8.2

# 工具库
python-dotenv==1.0.0
click==8.1.7
rich==13.7.0

# 测试相关（开发环境）
pytest==7.4.3
pytest-asyncio==0.21.1
httpx==0.25.2

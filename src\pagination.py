"""
分页工具模块
"""
from typing import Any, Generic, Optional, Sequence, TypeVar
from math import ceil

from fastapi import Query
from pydantic import BaseModel, Field
from sqlalchemy import func, select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.sql import Select

from src.constants import DEFAULT_PAGINATION_LIMIT, DEFAULT_PAGINATION_OFFSET

T = TypeVar("T")


class PaginationParams(BaseModel):
    """分页参数"""
    
    page: int = Field(default=1, ge=1, description="页码")
    size: int = Field(default=DEFAULT_PAGINATION_LIMIT, ge=1, le=100, description="每页数量")
    
    @property
    def offset(self) -> int:
        """计算偏移量"""
        return (self.page - 1) * self.size
    
    @property
    def limit(self) -> int:
        """获取限制数量"""
        return self.size


class PaginatedResponse(BaseModel, Generic[T]):
    """分页响应模型"""
    
    items: Sequence[T] = Field(description="数据项")
    total: int = Field(description="总数量")
    page: int = Field(description="当前页码")
    size: int = Field(description="每页数量")
    pages: int = Field(description="总页数")
    has_next: bool = Field(description="是否有下一页")
    has_prev: bool = Field(description="是否有上一页")
    
    @classmethod
    def create(
        cls,
        items: Sequence[T],
        total: int,
        page: int,
        size: int,
    ) -> "PaginatedResponse[T]":
        """创建分页响应"""
        pages = ceil(total / size) if size > 0 else 0
        
        return cls(
            items=items,
            total=total,
            page=page,
            size=size,
            pages=pages,
            has_next=page < pages,
            has_prev=page > 1,
        )


def get_pagination_params(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(DEFAULT_PAGINATION_LIMIT, ge=1, le=100, description="每页数量"),
) -> PaginationParams:
    """获取分页参数的依赖项"""
    return PaginationParams(page=page, size=size)


async def paginate(
    db: AsyncSession,
    query: Select,
    params: PaginationParams,
) -> tuple[Sequence[Any], int]:
    """
    执行分页查询
    
    Args:
        db: 数据库会话
        query: 查询语句
        params: 分页参数
    
    Returns:
        tuple: (数据项列表, 总数量)
    """
    # 获取总数量
    count_query = select(func.count()).select_from(query.subquery())
    total_result = await db.execute(count_query)
    total = total_result.scalar() or 0
    
    # 获取分页数据
    paginated_query = query.offset(params.offset).limit(params.limit)
    result = await db.execute(paginated_query)
    items = result.scalars().all()
    
    return items, total


async def paginate_response(
    db: AsyncSession,
    query: Select,
    params: PaginationParams,
) -> PaginatedResponse[Any]:
    """
    执行分页查询并返回分页响应
    
    Args:
        db: 数据库会话
        query: 查询语句
        params: 分页参数
    
    Returns:
        PaginatedResponse: 分页响应对象
    """
    items, total = await paginate(db, query, params)
    
    return PaginatedResponse.create(
        items=items,
        total=total,
        page=params.page,
        size=params.size,
    )

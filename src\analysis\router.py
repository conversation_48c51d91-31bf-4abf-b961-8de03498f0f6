"""
股票分析相关的路由
"""
from typing import Any, List
from uuid import UUID

from fastapi import APIRouter, BackgroundTasks, Depends, status

from src.analysis.dependencies import (
    get_analysis_by_id,
    get_analysis_query_params,
    get_template_by_id,
    get_template_query_params,
    get_user_analysis,
    get_user_template,
)
from src.analysis.models import Analysis, AnalysisTemplate
from src.analysis.schemas import (
    AnalysisCreate,
    AnalysisQuery,
    AnalysisResponse,
    AnalysisTemplateCreate,
    AnalysisTemplateResponse,
    AnalysisTemplateUpdate,
    AnalysisUpdate,
)
from src.analysis.service import AnalysisService, get_analysis_service
from src.auth.dependencies import get_current_active_user, require_admin
from src.auth.models import User
from src.pagination import PaginatedResponse, get_pagination_params, PaginationParams

router = APIRouter(prefix="/analysis", tags=["股票分析"])


@router.get(
    "/",
    response_model=PaginatedResponse[AnalysisResponse],
    summary="获取分析列表",
    description="获取分析列表，支持搜索和分页",
)
async def get_analyses(
    query_params: AnalysisQuery = Depends(get_analysis_query_params),
    pagination: PaginationParams = Depends(get_pagination_params),
    current_user: User = Depends(get_current_active_user),
    analysis_service: AnalysisService = Depends(get_analysis_service),
) -> Any:
    """获取分析列表"""
    # 普通用户只能看到自己的分析，管理员可以看到所有分析
    user_id = None if current_user.role == "admin" else current_user.id
    
    analyses, total = await analysis_service.search_analyses(
        query_params, pagination, user_id
    )
    
    return PaginatedResponse.create(
        items=analyses,
        total=total,
        page=pagination.page,
        size=pagination.size,
    )


@router.get(
    "/{analysis_id}",
    response_model=AnalysisResponse,
    summary="获取分析详情",
    description="根据分析ID获取分析详细信息",
)
async def get_analysis(
    analysis: Analysis = Depends(get_user_analysis),
) -> Any:
    """获取分析详情"""
    return analysis


@router.post(
    "/",
    response_model=AnalysisResponse,
    status_code=status.HTTP_201_CREATED,
    summary="创建分析",
    description="创建新的股票分析",
)
async def create_analysis(
    analysis_create: AnalysisCreate,
    current_user: User = Depends(get_current_active_user),
    analysis_service: AnalysisService = Depends(get_analysis_service),
) -> Any:
    """创建分析"""
    analysis = await analysis_service.create_analysis(analysis_create, current_user.id)
    return analysis


@router.put(
    "/{analysis_id}",
    response_model=AnalysisResponse,
    summary="更新分析",
    description="更新分析信息",
)
async def update_analysis(
    analysis_update: AnalysisUpdate,
    analysis: Analysis = Depends(get_user_analysis),
    analysis_service: AnalysisService = Depends(get_analysis_service),
) -> Any:
    """更新分析"""
    updated_analysis = await analysis_service.update_analysis(analysis.id, analysis_update)
    return updated_analysis


@router.delete(
    "/{analysis_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="删除分析",
    description="删除分析记录",
)
async def delete_analysis(
    analysis: Analysis = Depends(get_user_analysis),
    analysis_service: AnalysisService = Depends(get_analysis_service),
) -> None:
    """删除分析"""
    await analysis_service.delete_analysis(analysis.id)


@router.post(
    "/{analysis_id}/execute",
    response_model=AnalysisResponse,
    summary="执行分析",
    description="执行股票分析计算",
)
async def execute_analysis(
    background_tasks: BackgroundTasks,
    analysis: Analysis = Depends(get_user_analysis),
    analysis_service: AnalysisService = Depends(get_analysis_service),
) -> Any:
    """执行分析"""
    # 在后台任务中执行分析，避免阻塞请求
    background_tasks.add_task(analysis_service.execute_analysis, analysis.id)
    
    # 立即返回分析对象（状态为pending或running）
    analysis.status = "running"
    return analysis


@router.get(
    "/{analysis_id}/execute",
    response_model=AnalysisResponse,
    summary="同步执行分析",
    description="同步执行股票分析计算（等待结果）",
)
async def execute_analysis_sync(
    analysis: Analysis = Depends(get_user_analysis),
    analysis_service: AnalysisService = Depends(get_analysis_service),
) -> Any:
    """同步执行分析"""
    # 同步执行分析
    result = await analysis_service.execute_analysis(analysis.id)
    return result


# 模板相关路由
@router.get(
    "/templates/",
    response_model=List[AnalysisTemplateResponse],
    summary="获取分析模板列表",
    description="获取可用的分析模板列表",
)
async def get_templates(
    query_params: dict = Depends(get_template_query_params),
    current_user: User = Depends(get_current_active_user),
    analysis_service: AnalysisService = Depends(get_analysis_service),
) -> Any:
    """获取分析模板列表"""
    user_id = None if query_params["public_only"] else current_user.id
    
    templates = await analysis_service.get_templates(
        analysis_type=query_params["analysis_type"],
        user_id=user_id,
        public_only=query_params["public_only"],
    )
    
    return templates


@router.get(
    "/templates/{template_id}",
    response_model=AnalysisTemplateResponse,
    summary="获取模板详情",
    description="根据模板ID获取模板详细信息",
)
async def get_template(
    template: AnalysisTemplate = Depends(get_user_template),
) -> Any:
    """获取模板详情"""
    return template


@router.post(
    "/templates/",
    response_model=AnalysisTemplateResponse,
    status_code=status.HTTP_201_CREATED,
    summary="创建分析模板",
    description="创建新的分析模板",
)
async def create_template(
    template_create: AnalysisTemplateCreate,
    current_user: User = Depends(get_current_active_user),
    analysis_service: AnalysisService = Depends(get_analysis_service),
) -> Any:
    """创建分析模板"""
    template = await analysis_service.create_template(template_create, current_user.id)
    return template


@router.put(
    "/templates/{template_id}",
    response_model=AnalysisTemplateResponse,
    summary="更新模板",
    description="更新分析模板信息",
)
async def update_template(
    template_update: AnalysisTemplateUpdate,
    template: AnalysisTemplate = Depends(get_user_template),
    analysis_service: AnalysisService = Depends(get_analysis_service),
) -> Any:
    """更新模板"""
    # 这里需要实现模板更新逻辑
    # 为了简化，暂时返回原模板
    return template


@router.delete(
    "/templates/{template_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="删除模板",
    description="删除分析模板",
)
async def delete_template(
    template: AnalysisTemplate = Depends(get_user_template),
    analysis_service: AnalysisService = Depends(get_analysis_service),
) -> None:
    """删除模板"""
    # 这里需要实现模板删除逻辑
    pass

"""
全局配置文件
"""
from functools import lru_cache
from typing import Optional

from pydantic import PostgresDsn, RedisDsn, field_validator
from pydantic_settings import BaseSettings

from src.constants import Environment


class Config(BaseSettings):
    """全局配置类"""
    
    # 数据库配置
    DATABASE_URL: PostgresDsn
    REDIS_URL: Optional[RedisDsn] = None
    
    # 应用配置
    APP_NAME: str = "股票分析系统"
    APP_VERSION: str = "1.0.0"
    DEBUG: bool = False
    
    # 环境配置
    ENVIRONMENT: Environment = Environment.PRODUCTION
    
    # 安全配置
    SECRET_KEY: str
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7
    
    # CORS配置
    CORS_ORIGINS: list[str] = ["http://localhost:3000", "http://localhost:8080"]
    CORS_ORIGINS_REGEX: Optional[str] = None
    CORS_HEADERS: list[str] = ["*"]
    
    # API配置
    API_V1_STR: str = "/api/v1"
    
    # 股票数据源配置
    STOCK_DATA_SOURCE: str = "tushare"  # tushare, akshare, yfinance
    TUSHARE_TOKEN: Optional[str] = None
    
    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    # 分页配置
    DEFAULT_PAGE_SIZE: int = 20
    MAX_PAGE_SIZE: int = 100
    
    @field_validator("CORS_ORIGINS", mode="before")
    @classmethod
    def assemble_cors_origins(cls, v: str | list[str]) -> list[str]:
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, (list, str)):
            return v
        raise ValueError(v)

    class Config:
        env_file = ".env"
        case_sensitive = True


@lru_cache()
def get_settings() -> Config:
    """获取配置实例（单例模式）"""
    return Config()


# 全局配置实例
settings = get_settings()

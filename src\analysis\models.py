"""
股票分析相关的数据库模型
"""
from datetime import datetime
from typing import Optional
from uuid import UUID

from sqlalchemy import DateTime, ForeignKey, JSON, String, Text
from sqlalchemy.dialects.postgresql import UUID as PostgresUUID
from sqlalchemy.orm import Mapped, mapped_column, relationship

from src.constants import AnalysisType
from src.models import BaseModel


class Analysis(BaseModel):
    """股票分析模型"""
    
    __tablename__ = "analyses"
    
    # 基本信息
    title: Mapped[str] = mapped_column(
        String(200),
        nullable=False,
        comment="分析标题"
    )
    
    symbol: Mapped[str] = mapped_column(
        String(20),
        nullable=False,
        index=True,
        comment="股票代码"
    )
    
    analysis_type: Mapped[AnalysisType] = mapped_column(
        String(20),
        nullable=False,
        comment="分析类型"
    )
    
    # 用户信息
    user_id: Mapped[UUID] = mapped_column(
        PostgresUUID(as_uuid=True),
        ForeignKey("users.id"),
        nullable=False,
        comment="创建用户ID"
    )
    
    # 分析内容
    description: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        comment="分析描述"
    )
    
    content: Mapped[Optional[dict]] = mapped_column(
        JSON,
        nullable=True,
        comment="分析内容（JSON格式）"
    )
    
    # 分析结果
    result: Mapped[Optional[dict]] = mapped_column(
        JSON,
        nullable=True,
        comment="分析结果（JSON格式）"
    )
    
    # 分析参数
    parameters: Mapped[Optional[dict]] = mapped_column(
        JSON,
        nullable=True,
        comment="分析参数（JSON格式）"
    )
    
    # 状态信息
    status: Mapped[str] = mapped_column(
        String(20),
        default="pending",
        nullable=False,
        comment="分析状态：pending, running, completed, failed"
    )
    
    # 时间信息
    started_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        comment="开始时间"
    )
    
    completed_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        comment="完成时间"
    )
    
    # 错误信息
    error_message: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        comment="错误信息"
    )
    
    # 关联关系
    user = relationship("User", back_populates="analyses")
    results = relationship("AnalysisResult", back_populates="analysis")
    
    def __repr__(self) -> str:
        return f"<Analysis(id={self.id}, title={self.title}, symbol={self.symbol}, type={self.analysis_type})>"


class AnalysisTemplate(BaseModel):
    """分析模板模型"""
    
    __tablename__ = "analysis_templates"
    
    # 基本信息
    name: Mapped[str] = mapped_column(
        String(100),
        nullable=False,
        comment="模板名称"
    )
    
    analysis_type: Mapped[AnalysisType] = mapped_column(
        String(20),
        nullable=False,
        comment="分析类型"
    )
    
    # 模板内容
    description: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        comment="模板描述"
    )
    
    template_config: Mapped[dict] = mapped_column(
        JSON,
        nullable=False,
        comment="模板配置（JSON格式）"
    )
    
    # 默认参数
    default_parameters: Mapped[Optional[dict]] = mapped_column(
        JSON,
        nullable=True,
        comment="默认参数（JSON格式）"
    )
    
    # 创建者信息
    created_by: Mapped[UUID] = mapped_column(
        PostgresUUID(as_uuid=True),
        ForeignKey("users.id"),
        nullable=False,
        comment="创建者ID"
    )
    
    # 状态信息
    is_public: Mapped[bool] = mapped_column(
        default=False,
        nullable=False,
        comment="是否公开"
    )
    
    is_active: Mapped[bool] = mapped_column(
        default=True,
        nullable=False,
        comment="是否激活"
    )
    
    # 关联关系
    creator = relationship("User", back_populates="analysis_templates")
    
    def __repr__(self) -> str:
        return f"<AnalysisTemplate(id={self.id}, name={self.name}, type={self.analysis_type})>"


class AnalysisResult(BaseModel):
    """分析结果详情模型"""
    
    __tablename__ = "analysis_results"
    
    # 关联分析
    analysis_id: Mapped[UUID] = mapped_column(
        PostgresUUID(as_uuid=True),
        ForeignKey("analyses.id"),
        nullable=False,
        comment="分析ID"
    )
    
    # 结果类型
    result_type: Mapped[str] = mapped_column(
        String(50),
        nullable=False,
        comment="结果类型：chart, table, text, metric"
    )
    
    # 结果标题
    title: Mapped[str] = mapped_column(
        String(200),
        nullable=False,
        comment="结果标题"
    )
    
    # 结果数据
    data: Mapped[dict] = mapped_column(
        JSON,
        nullable=False,
        comment="结果数据（JSON格式）"
    )
    
    # 结果配置
    config: Mapped[Optional[dict]] = mapped_column(
        JSON,
        nullable=True,
        comment="结果配置（JSON格式）"
    )
    
    # 排序顺序
    order_index: Mapped[int] = mapped_column(
        default=0,
        nullable=False,
        comment="排序索引"
    )
    
    # 关联关系
    analysis = relationship("Analysis", back_populates="results")
    
    def __repr__(self) -> str:
        return f"<AnalysisResult(id={self.id}, analysis_id={self.analysis_id}, type={self.result_type})>"

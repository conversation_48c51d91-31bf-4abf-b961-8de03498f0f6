"""
股票数据相关的路由
"""
from typing import Any
from uuid import UUID

from fastapi import APIRouter, Depends, Query, status

from src.auth.dependencies import get_current_active_user, require_admin
from src.auth.models import User
from src.pagination import PaginatedResponse, get_pagination_params, PaginationParams
from src.stocks.dependencies import (
    get_price_query_params,
    get_stock_by_id,
    get_stock_by_symbol,
    get_stock_query_params,
)
from src.stocks.models import Stock
from src.stocks.schemas import (
    PriceQuery,
    StockCreate,
    StockDividendCreate,
    StockDividendResponse,
    StockPriceCreate,
    StockPriceResponse,
    StockQuery,
    StockResponse,
    StockUpdate,
)
from src.stocks.service import StockService, get_stock_service

router = APIRouter(prefix="/stocks", tags=["股票数据"])


@router.get(
    "/",
    response_model=PaginatedResponse[StockResponse],
    summary="获取股票列表",
    description="获取股票列表，支持搜索和分页",
)
async def get_stocks(
    query_params: StockQuery = Depends(get_stock_query_params),
    pagination: PaginationParams = Depends(get_pagination_params),
    stock_service: StockService = Depends(get_stock_service),
) -> Any:
    """获取股票列表"""
    stocks, total = await stock_service.search_stocks(query_params, pagination)
    
    return PaginatedResponse.create(
        items=stocks,
        total=total,
        page=pagination.page,
        size=pagination.size,
    )


@router.get(
    "/{symbol}",
    response_model=StockResponse,
    summary="获取股票详情",
    description="根据股票代码获取股票详细信息",
)
async def get_stock(
    stock: Stock = Depends(get_stock_by_symbol),
) -> Any:
    """获取股票详情"""
    return stock


@router.post(
    "/",
    response_model=StockResponse,
    status_code=status.HTTP_201_CREATED,
    summary="创建股票",
    description="创建新的股票记录（需要管理员权限）",
)
async def create_stock(
    stock_create: StockCreate,
    current_user: User = Depends(require_admin),
    stock_service: StockService = Depends(get_stock_service),
) -> Any:
    """创建股票"""
    stock = await stock_service.create_stock(stock_create)
    return stock


@router.put(
    "/{stock_id}",
    response_model=StockResponse,
    summary="更新股票信息",
    description="更新股票信息（需要管理员权限）",
)
async def update_stock(
    stock_update: StockUpdate,
    stock: Stock = Depends(get_stock_by_id),
    current_user: User = Depends(require_admin),
    stock_service: StockService = Depends(get_stock_service),
) -> Any:
    """更新股票信息"""
    updated_stock = await stock_service.update_stock(stock.id, stock_update)
    return updated_stock


@router.delete(
    "/{stock_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="删除股票",
    description="删除股票记录（需要管理员权限）",
)
async def delete_stock(
    stock: Stock = Depends(get_stock_by_id),
    current_user: User = Depends(require_admin),
    stock_service: StockService = Depends(get_stock_service),
) -> None:
    """删除股票"""
    await stock_service.delete_stock(stock.id)


@router.get(
    "/{symbol}/prices",
    response_model=PaginatedResponse[StockPriceResponse],
    summary="获取股票价格历史",
    description="获取股票的历史价格数据",
)
async def get_stock_prices(
    price_query: PriceQuery = Depends(get_price_query_params),
    pagination: PaginationParams = Depends(get_pagination_params),
    stock_service: StockService = Depends(get_stock_service),
) -> Any:
    """获取股票价格历史"""
    prices, total = await stock_service.get_stock_prices(price_query, pagination)
    
    return PaginatedResponse.create(
        items=prices,
        total=total,
        page=pagination.page,
        size=pagination.size,
    )


@router.get(
    "/{symbol}/latest-price",
    response_model=StockPriceResponse,
    summary="获取最新价格",
    description="获取股票的最新价格数据",
)
async def get_latest_price(
    symbol: str,
    stock_service: StockService = Depends(get_stock_service),
) -> Any:
    """获取最新价格"""
    latest_price = await stock_service.get_latest_price(symbol)
    if not latest_price:
        from src.exceptions import NotFoundException
        raise NotFoundException(f"No price data found for stock {symbol}")
    
    return latest_price


@router.post(
    "/{symbol}/prices",
    response_model=StockPriceResponse,
    status_code=status.HTTP_201_CREATED,
    summary="添加价格数据",
    description="为股票添加价格数据（需要管理员权限）",
)
async def create_stock_price(
    price_create: StockPriceCreate,
    current_user: User = Depends(require_admin),
    stock_service: StockService = Depends(get_stock_service),
) -> Any:
    """添加价格数据"""
    price = await stock_service.create_stock_price(price_create)
    return price


@router.post(
    "/prices/batch",
    response_model=list[StockPriceResponse],
    status_code=status.HTTP_201_CREATED,
    summary="批量添加价格数据",
    description="批量添加股票价格数据（需要管理员权限）",
)
async def batch_create_stock_prices(
    prices: list[StockPriceCreate],
    current_user: User = Depends(require_admin),
    stock_service: StockService = Depends(get_stock_service),
) -> Any:
    """批量添加价格数据"""
    created_prices = await stock_service.batch_create_stock_prices(prices)
    return created_prices


@router.get(
    "/{symbol}/dividends",
    response_model=list[StockDividendResponse],
    summary="获取分红数据",
    description="获取股票的分红历史数据",
)
async def get_stock_dividends(
    symbol: str,
    start_date: str = Query(None, description="开始日期 (YYYY-MM-DD)"),
    end_date: str = Query(None, description="结束日期 (YYYY-MM-DD)"),
    stock_service: StockService = Depends(get_stock_service),
) -> Any:
    """获取分红数据"""
    from datetime import datetime
    
    start_date_obj = None
    end_date_obj = None
    
    if start_date:
        try:
            start_date_obj = datetime.strptime(start_date, "%Y-%m-%d").date()
        except ValueError:
            raise ValueError("Invalid start_date format. Use YYYY-MM-DD")
    
    if end_date:
        try:
            end_date_obj = datetime.strptime(end_date, "%Y-%m-%d").date()
        except ValueError:
            raise ValueError("Invalid end_date format. Use YYYY-MM-DD")
    
    dividends = await stock_service.get_stock_dividends(
        symbol, start_date_obj, end_date_obj
    )
    return dividends


@router.post(
    "/{symbol}/dividends",
    response_model=StockDividendResponse,
    status_code=status.HTTP_201_CREATED,
    summary="添加分红数据",
    description="为股票添加分红数据（需要管理员权限）",
)
async def create_stock_dividend(
    dividend_create: StockDividendCreate,
    current_user: User = Depends(require_admin),
    stock_service: StockService = Depends(get_stock_service),
) -> Any:
    """添加分红数据"""
    dividend = await stock_service.create_stock_dividend(dividend_create)
    return dividend

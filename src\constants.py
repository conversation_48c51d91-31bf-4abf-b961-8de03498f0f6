"""
全局常量定义
"""
from enum import Enum


class Environment(str, Enum):
    """环境枚举"""
    DEVELOPMENT = "development"
    TESTING = "testing"
    PRODUCTION = "production"


class UserRole(str, Enum):
    """用户角色枚举"""
    ADMIN = "admin"
    USER = "user"
    PREMIUM = "premium"


class StockMarket(str, Enum):
    """股票市场枚举"""
    SSE = "SSE"  # 上海证券交易所
    SZSE = "SZSE"  # 深圳证券交易所
    NASDAQ = "NASDAQ"  # 纳斯达克
    NYSE = "NYSE"  # 纽约证券交易所


class AnalysisType(str, Enum):
    """分析类型枚举"""
    TECHNICAL = "technical"  # 技术分析
    FUNDAMENTAL = "fundamental"  # 基本面分析
    SENTIMENT = "sentiment"  # 情绪分析
    QUANTITATIVE = "quantitative"  # 量化分析


class TimeFrame(str, Enum):
    """时间周期枚举"""
    MINUTE_1 = "1m"
    MINUTE_5 = "5m"
    MINUTE_15 = "15m"
    MINUTE_30 = "30m"
    HOUR_1 = "1h"
    HOUR_4 = "4h"
    DAY_1 = "1d"
    WEEK_1 = "1w"
    MONTH_1 = "1M"


# HTTP状态码
HTTP_200_OK = 200
HTTP_201_CREATED = 201
HTTP_400_BAD_REQUEST = 400
HTTP_401_UNAUTHORIZED = 401
HTTP_403_FORBIDDEN = 403
HTTP_404_NOT_FOUND = 404
HTTP_422_UNPROCESSABLE_ENTITY = 422
HTTP_500_INTERNAL_SERVER_ERROR = 500

# 错误代码
class ErrorCode:
    """错误代码常量"""
    # 通用错误
    VALIDATION_ERROR = "VALIDATION_ERROR"
    INTERNAL_ERROR = "INTERNAL_ERROR"
    NOT_FOUND = "NOT_FOUND"
    
    # 认证错误
    INVALID_CREDENTIALS = "INVALID_CREDENTIALS"
    TOKEN_EXPIRED = "TOKEN_EXPIRED"
    INSUFFICIENT_PERMISSIONS = "INSUFFICIENT_PERMISSIONS"
    
    # 用户错误
    USER_NOT_FOUND = "USER_NOT_FOUND"
    USER_ALREADY_EXISTS = "USER_ALREADY_EXISTS"
    USER_INACTIVE = "USER_INACTIVE"
    
    # 股票数据错误
    STOCK_NOT_FOUND = "STOCK_NOT_FOUND"
    DATA_SOURCE_ERROR = "DATA_SOURCE_ERROR"
    INVALID_SYMBOL = "INVALID_SYMBOL"
    
    # 分析错误
    ANALYSIS_FAILED = "ANALYSIS_FAILED"
    INSUFFICIENT_DATA = "INSUFFICIENT_DATA"


# 默认值
DEFAULT_PAGINATION_LIMIT = 20
DEFAULT_PAGINATION_OFFSET = 0

# 缓存键前缀
CACHE_KEY_PREFIX = {
    "user": "user:",
    "stock": "stock:",
    "analysis": "analysis:",
    "token": "token:"
}

# 缓存过期时间（秒）
CACHE_EXPIRE_TIME = {
    "short": 300,  # 5分钟
    "medium": 1800,  # 30分钟
    "long": 3600,  # 1小时
    "daily": 86400  # 24小时
}

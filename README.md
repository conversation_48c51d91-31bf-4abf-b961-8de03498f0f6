# 股票分析系统后端

基于 FastAPI 的股票分析系统后端 API，提供股票数据管理、技术分析、基本面分析等功能。

## 功能特性

- 🚀 **高性能**: 基于 FastAPI 和异步编程
- 📊 **股票数据**: 支持多种数据源（Tushare、AKShare、yfinance）
- 🔍 **多种分析**: 技术分析、基本面分析、量化分析、情绪分析
- 🔐 **用户认证**: JWT 令牌认证和权限管理
- 📈 **数据可视化**: 支持图表和数据导出
- 🗄️ **数据库**: PostgreSQL + Redis 缓存
- 📝 **API 文档**: 自动生成的 OpenAPI 文档
- 🧪 **测试覆盖**: 完整的单元测试和集成测试

## 技术栈

- **框架**: FastAPI 0.104+
- **数据库**: PostgreSQL + SQLAlchemy 2.0 (异步)
- **缓存**: Redis
- **认证**: JWT + bcrypt
- **数据处理**: Pandas + NumPy
- **技术分析**: TA-Lib + ta
- **可视化**: Plotly + Matplotlib
- **部署**: Docker + Uvicorn

## 项目结构

```
backend/
├── src/                    # 源代码目录
│   ├── auth/              # 用户认证模块
│   ├── stocks/            # 股票数据模块
│   ├── analysis/          # 分析模块
│   ├── config.py          # 全局配置
│   ├── database.py        # 数据库配置
│   ├── exceptions.py      # 异常定义
│   ├── models.py          # 基础模型
│   ├── pagination.py      # 分页工具
│   └── main.py           # 应用入口
├── tests/                 # 测试目录
├── alembic/              # 数据库迁移
├── requirements/         # 依赖文件
├── templates/            # 模板文件
├── .env.example         # 环境变量示例
├── alembic.ini          # Alembic 配置
└── README.md            # 项目说明
```

## 快速开始

### 1. 环境准备

```bash
# 克隆项目
git clone <repository-url>
cd backend

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows

# 安装依赖
pip install -r requirements/dev.txt
```

### 2. 配置环境变量

```bash
# 复制环境变量文件
cp .env.example .env

# 编辑 .env 文件，配置数据库连接等信息
```

### 3. 数据库设置

```bash
# 创建数据库迁移
alembic revision --autogenerate -m "Initial migration"

# 执行迁移
alembic upgrade head
```

### 4. 启动应用

```bash
# 开发模式启动
python -m src.main

# 或使用 uvicorn
uvicorn src.main:app --reload --host 0.0.0.0 --port 8000
```

### 5. 访问 API 文档

- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## API 端点

### 认证相关
- `POST /api/v1/auth/register` - 用户注册
- `POST /api/v1/auth/login` - 用户登录
- `GET /api/v1/auth/me` - 获取当前用户信息
- `PUT /api/v1/auth/me` - 更新用户信息

### 股票数据
- `GET /api/v1/stocks/` - 获取股票列表
- `GET /api/v1/stocks/{symbol}` - 获取股票详情
- `GET /api/v1/stocks/{symbol}/prices` - 获取价格历史
- `GET /api/v1/stocks/{symbol}/latest-price` - 获取最新价格

### 分析功能
- `GET /api/v1/analysis/` - 获取分析列表
- `POST /api/v1/analysis/` - 创建分析
- `POST /api/v1/analysis/{id}/execute` - 执行分析
- `GET /api/v1/analysis/templates/` - 获取分析模板

## 开发指南

### 代码规范

项目使用以下工具确保代码质量：

- **Black**: 代码格式化
- **isort**: 导入排序
- **Ruff**: 代码检查
- **MyPy**: 类型检查

```bash
# 格式化代码
black src/
isort src/

# 代码检查
ruff check src/
mypy src/
```

### 运行测试

```bash
# 运行所有测试
pytest

# 运行测试并生成覆盖率报告
pytest --cov=src --cov-report=html
```

### 数据库迁移

```bash
# 创建新的迁移
alembic revision --autogenerate -m "描述信息"

# 执行迁移
alembic upgrade head

# 回滚迁移
alembic downgrade -1
```

## 部署

### Docker 部署

```bash
# 构建镜像
docker build -t stock-analysis-backend .

# 运行容器
docker run -p 8000:8000 --env-file .env stock-analysis-backend
```

### 生产环境

```bash
# 安装生产依赖
pip install -r requirements/prod.txt

# 使用 Gunicorn 启动
gunicorn src.main:app -w 4 -k uvicorn.workers.UvicornWorker
```

## 配置说明

主要配置项说明：

- `DATABASE_URL`: PostgreSQL 数据库连接字符串
- `REDIS_URL`: Redis 连接字符串
- `SECRET_KEY`: JWT 令牌加密密钥
- `TUSHARE_TOKEN`: Tushare 数据源 API 令牌
- `CORS_ORIGINS`: 允许的跨域来源

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 联系方式

如有问题或建议，请通过以下方式联系：

- 提交 Issue
- 发送邮件到 [<EMAIL>]

## 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 基础用户认证功能
- 股票数据管理
- 基本分析功能

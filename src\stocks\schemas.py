"""
股票数据相关的Pydantic模型
"""
from datetime import date, datetime
from decimal import Decimal
from typing import Optional
from uuid import UUID

from pydantic import BaseModel, Field, field_validator

from src.constants import StockMarket, TimeFrame


class StockBase(BaseModel):
    """股票基础模型"""
    
    symbol: str = Field(description="股票代码", max_length=20)
    name: str = Field(description="股票名称", max_length=100)
    market: StockMarket = Field(description="交易市场")
    
    @field_validator("symbol")
    @classmethod
    def validate_symbol(cls, v: str) -> str:
        """验证股票代码格式"""
        return v.upper().strip()


class StockCreate(StockBase):
    """创建股票模型"""
    
    company_name: Optional[str] = Field(None, description="公司全称", max_length=200)
    industry: Optional[str] = Field(None, description="所属行业", max_length=100)
    sector: Optional[str] = Field(None, description="所属板块", max_length=100)
    description: Optional[str] = Field(None, description="公司描述")
    list_date: Optional[date] = Field(None, description="上市日期")


class StockUpdate(BaseModel):
    """更新股票模型"""
    
    name: Optional[str] = Field(None, description="股票名称", max_length=100)
    company_name: Optional[str] = Field(None, description="公司全称", max_length=200)
    industry: Optional[str] = Field(None, description="所属行业", max_length=100)
    sector: Optional[str] = Field(None, description="所属板块", max_length=100)
    description: Optional[str] = Field(None, description="公司描述")
    market_cap: Optional[Decimal] = Field(None, description="市值")
    total_shares: Optional[Decimal] = Field(None, description="总股本")
    float_shares: Optional[Decimal] = Field(None, description="流通股本")
    is_active: Optional[bool] = Field(None, description="是否活跃交易")


class StockResponse(StockBase):
    """股票响应模型"""
    
    id: UUID = Field(description="股票ID")
    company_name: Optional[str] = Field(None, description="公司全称")
    industry: Optional[str] = Field(None, description="所属行业")
    sector: Optional[str] = Field(None, description="所属板块")
    description: Optional[str] = Field(None, description="公司描述")
    list_date: Optional[date] = Field(None, description="上市日期")
    market_cap: Optional[Decimal] = Field(None, description="市值")
    total_shares: Optional[Decimal] = Field(None, description="总股本")
    float_shares: Optional[Decimal] = Field(None, description="流通股本")
    is_active: bool = Field(description="是否活跃交易")
    created_at: datetime = Field(description="创建时间")
    updated_at: datetime = Field(description="更新时间")
    
    class Config:
        from_attributes = True


class StockPriceBase(BaseModel):
    """股票价格基础模型"""
    
    symbol: str = Field(description="股票代码", max_length=20)
    trade_date: date = Field(description="交易日期")
    
    @field_validator("symbol")
    @classmethod
    def validate_symbol(cls, v: str) -> str:
        """验证股票代码格式"""
        return v.upper().strip()


class StockPriceCreate(StockPriceBase):
    """创建股票价格模型"""
    
    trade_time: Optional[datetime] = Field(None, description="交易时间")
    open_price: Optional[Decimal] = Field(None, description="开盘价", ge=0)
    high_price: Optional[Decimal] = Field(None, description="最高价", ge=0)
    low_price: Optional[Decimal] = Field(None, description="最低价", ge=0)
    close_price: Optional[Decimal] = Field(None, description="收盘价", ge=0)
    volume: Optional[Decimal] = Field(None, description="成交量", ge=0)
    amount: Optional[Decimal] = Field(None, description="成交额", ge=0)
    change: Optional[Decimal] = Field(None, description="涨跌额")
    change_pct: Optional[Decimal] = Field(None, description="涨跌幅(%)")
    turnover_rate: Optional[Decimal] = Field(None, description="换手率(%)", ge=0)
    pe_ratio: Optional[Decimal] = Field(None, description="市盈率")
    pb_ratio: Optional[Decimal] = Field(None, description="市净率")


class StockPriceResponse(StockPriceBase):
    """股票价格响应模型"""
    
    id: UUID = Field(description="价格数据ID")
    trade_time: Optional[datetime] = Field(None, description="交易时间")
    open_price: Optional[Decimal] = Field(None, description="开盘价")
    high_price: Optional[Decimal] = Field(None, description="最高价")
    low_price: Optional[Decimal] = Field(None, description="最低价")
    close_price: Optional[Decimal] = Field(None, description="收盘价")
    volume: Optional[Decimal] = Field(None, description="成交量")
    amount: Optional[Decimal] = Field(None, description="成交额")
    change: Optional[Decimal] = Field(None, description="涨跌额")
    change_pct: Optional[Decimal] = Field(None, description="涨跌幅(%)")
    turnover_rate: Optional[Decimal] = Field(None, description="换手率(%)")
    pe_ratio: Optional[Decimal] = Field(None, description="市盈率")
    pb_ratio: Optional[Decimal] = Field(None, description="市净率")
    created_at: datetime = Field(description="创建时间")
    updated_at: datetime = Field(description="更新时间")
    
    class Config:
        from_attributes = True


class StockDividendBase(BaseModel):
    """股票分红基础模型"""
    
    symbol: str = Field(description="股票代码", max_length=20)
    ex_date: date = Field(description="除权除息日")
    
    @field_validator("symbol")
    @classmethod
    def validate_symbol(cls, v: str) -> str:
        """验证股票代码格式"""
        return v.upper().strip()


class StockDividendCreate(StockDividendBase):
    """创建股票分红模型"""
    
    record_date: Optional[date] = Field(None, description="股权登记日")
    pay_date: Optional[date] = Field(None, description="派息日")
    cash_dividend: Optional[Decimal] = Field(None, description="现金分红(每股)", ge=0)
    stock_dividend: Optional[Decimal] = Field(None, description="股票分红(每股)", ge=0)


class StockDividendResponse(StockDividendBase):
    """股票分红响应模型"""
    
    id: UUID = Field(description="分红数据ID")
    record_date: Optional[date] = Field(None, description="股权登记日")
    pay_date: Optional[date] = Field(None, description="派息日")
    cash_dividend: Optional[Decimal] = Field(None, description="现金分红(每股)")
    stock_dividend: Optional[Decimal] = Field(None, description="股票分红(每股)")
    created_at: datetime = Field(description="创建时间")
    updated_at: datetime = Field(description="更新时间")
    
    class Config:
        from_attributes = True


class StockQuery(BaseModel):
    """股票查询参数"""
    
    symbol: Optional[str] = Field(None, description="股票代码")
    name: Optional[str] = Field(None, description="股票名称")
    market: Optional[StockMarket] = Field(None, description="交易市场")
    industry: Optional[str] = Field(None, description="所属行业")
    sector: Optional[str] = Field(None, description="所属板块")
    is_active: Optional[bool] = Field(None, description="是否活跃交易")


class PriceQuery(BaseModel):
    """价格查询参数"""
    
    symbol: str = Field(description="股票代码")
    start_date: Optional[date] = Field(None, description="开始日期")
    end_date: Optional[date] = Field(None, description="结束日期")
    time_frame: Optional[TimeFrame] = Field(TimeFrame.DAY_1, description="时间周期")


class StockListResponse(BaseModel):
    """股票列表响应模型"""
    
    symbol: str = Field(description="股票代码")
    name: str = Field(description="股票名称")
    market: StockMarket = Field(description="交易市场")
    industry: Optional[str] = Field(None, description="所属行业")
    latest_price: Optional[Decimal] = Field(None, description="最新价格")
    change_pct: Optional[Decimal] = Field(None, description="涨跌幅(%)")
    volume: Optional[Decimal] = Field(None, description="成交量")
    market_cap: Optional[Decimal] = Field(None, description="市值")
    
    class Config:
        from_attributes = True

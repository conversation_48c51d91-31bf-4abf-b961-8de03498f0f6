"""
主应用测试
"""
import pytest
from fastapi.testclient import TestClient

from src.main import app

client = TestClient(app)


def test_root():
    """测试根路径"""
    response = client.get("/")
    assert response.status_code == 200
    data = response.json()
    assert "message" in data
    assert "version" in data


def test_health_check():
    """测试健康检查"""
    response = client.get("/health")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "healthy"
    assert "app_name" in data
    assert "version" in data
    assert "environment" in data


def test_docs_redirect():
    """测试文档重定向"""
    response = client.get("/docs")
    # 在测试环境中，文档应该可以访问
    assert response.status_code in [200, 307]  # 200 或重定向


def test_api_v1_prefix():
    """测试API v1前缀"""
    # 测试不存在的端点应该返回404
    response = client.get("/api/v1/nonexistent")
    assert response.status_code == 404

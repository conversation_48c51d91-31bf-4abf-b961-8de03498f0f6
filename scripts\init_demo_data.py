"""
初始化演示数据脚本
"""
import asyncio
import sys
from datetime import date, datetime, timedelta
from decimal import Decimal
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.auth.models import User
from src.auth.service import AuthService
from src.auth.schemas import UserCreate
from src.stocks.models import Stock, StockPrice
from src.stocks.schemas import StockCreate, StockPriceCreate
from src.stocks.service import StockService
from src.analysis.models import Analysis, AnalysisTemplate
from src.analysis.schemas import AnalysisCreate, AnalysisTemplateCreate
from src.analysis.service import AnalysisService
from src.constants import AnalysisType, StockMarket, UserRole
from src.database import AsyncSessionLocal, init_db
from src.config import settings


async def create_demo_users():
    """创建演示用户"""
    print("创建演示用户...")
    
    async with AsyncSessionLocal() as db:
        auth_service = AuthService(db)
        
        # 创建管理员用户
        try:
            admin_user = await auth_service.create_user(UserCreate(
                email="<EMAIL>",
                username="admin",
                full_name="系统管理员",
                password="Admin123456"
            ))
            admin_user.role = UserRole.ADMIN
            admin_user.is_verified = True
            await db.commit()
            print(f"✓ 创建管理员用户: {admin_user.email}")
        except Exception as e:
            print(f"✗ 管理员用户可能已存在: {e}")
        
        # 创建普通用户
        try:
            user = await auth_service.create_user(UserCreate(
                email="<EMAIL>",
                username="testuser",
                full_name="测试用户",
                password="User123456"
            ))
            user.is_verified = True
            await db.commit()
            print(f"✓ 创建普通用户: {user.email}")
        except Exception as e:
            print(f"✗ 普通用户可能已存在: {e}")
        
        # 创建高级用户
        try:
            premium_user = await auth_service.create_user(UserCreate(
                email="<EMAIL>",
                username="premiumuser",
                full_name="高级用户",
                password="Premium123456"
            ))
            premium_user.role = UserRole.PREMIUM
            premium_user.is_verified = True
            await db.commit()
            print(f"✓ 创建高级用户: {premium_user.email}")
        except Exception as e:
            print(f"✗ 高级用户可能已存在: {e}")


async def create_demo_stocks():
    """创建演示股票数据"""
    print("创建演示股票数据...")
    
    async with AsyncSessionLocal() as db:
        stock_service = StockService(db)
        
        # 演示股票列表
        demo_stocks = [
            {
                "symbol": "000001",
                "name": "平安银行",
                "market": StockMarket.SZSE,
                "company_name": "平安银行股份有限公司",
                "industry": "银行",
                "sector": "金融",
                "description": "中国领先的股份制商业银行之一",
                "list_date": date(1991, 4, 3),
            },
            {
                "symbol": "000002",
                "name": "万科A",
                "market": StockMarket.SZSE,
                "company_name": "万科企业股份有限公司",
                "industry": "房地产开发",
                "sector": "房地产",
                "description": "中国最大的专业住宅开发企业之一",
                "list_date": date(1991, 1, 29),
            },
            {
                "symbol": "600000",
                "name": "浦发银行",
                "market": StockMarket.SSE,
                "company_name": "上海浦东发展银行股份有限公司",
                "industry": "银行",
                "sector": "金融",
                "description": "全国性股份制商业银行",
                "list_date": date(1999, 11, 10),
            },
            {
                "symbol": "600036",
                "name": "招商银行",
                "market": StockMarket.SSE,
                "company_name": "招商银行股份有限公司",
                "industry": "银行",
                "sector": "金融",
                "description": "中国领先的零售银行",
                "list_date": date(2002, 4, 9),
            },
            {
                "symbol": "AAPL",
                "name": "Apple Inc.",
                "market": StockMarket.NASDAQ,
                "company_name": "Apple Inc.",
                "industry": "Technology",
                "sector": "Consumer Electronics",
                "description": "American multinational technology company",
                "list_date": date(1980, 12, 12),
            },
        ]
        
        for stock_data in demo_stocks:
            try:
                stock = await stock_service.create_stock(StockCreate(**stock_data))
                print(f"✓ 创建股票: {stock.symbol} - {stock.name}")
            except Exception as e:
                print(f"✗ 股票 {stock_data['symbol']} 可能已存在: {e}")


async def create_demo_prices():
    """创建演示价格数据"""
    print("创建演示价格数据...")
    
    async with AsyncSessionLocal() as db:
        stock_service = StockService(db)
        
        # 为每只股票创建最近30天的价格数据
        symbols = ["000001", "000002", "600000", "600036", "AAPL"]
        
        for symbol in symbols:
            try:
                stock = await stock_service.get_stock_by_symbol(symbol)
                if not stock:
                    continue
                
                # 生成30天的价格数据
                base_price = Decimal("50.00") if symbol.startswith("0") or symbol.startswith("6") else Decimal("150.00")
                prices = []
                
                for i in range(30):
                    trade_date = date.today() - timedelta(days=29-i)
                    
                    # 简单的价格模拟（随机波动）
                    import random
                    change_pct = (random.random() - 0.5) * 0.1  # -5% 到 +5%
                    price = base_price * (1 + Decimal(str(change_pct)))
                    
                    open_price = price * Decimal("0.995")
                    high_price = price * Decimal("1.02")
                    low_price = price * Decimal("0.98")
                    close_price = price
                    volume = Decimal(str(random.randint(1000000, 10000000)))
                    
                    price_data = StockPriceCreate(
                        symbol=symbol,
                        trade_date=trade_date,
                        open_price=open_price,
                        high_price=high_price,
                        low_price=low_price,
                        close_price=close_price,
                        volume=volume,
                        amount=volume * close_price,
                        change=close_price - base_price,
                        change_pct=(close_price - base_price) / base_price * 100,
                    )
                    prices.append(price_data)
                    base_price = close_price  # 下一天的基准价格
                
                # 批量创建价格数据
                created_prices = await stock_service.batch_create_stock_prices(prices)
                print(f"✓ 为股票 {symbol} 创建了 {len(created_prices)} 条价格数据")
                
            except Exception as e:
                print(f"✗ 创建股票 {symbol} 价格数据失败: {e}")


async def create_demo_templates():
    """创建演示分析模板"""
    print("创建演示分析模板...")
    
    async with AsyncSessionLocal() as db:
        analysis_service = AnalysisService(db)
        
        # 获取管理员用户
        from sqlalchemy import select
        result = await db.execute(select(User).where(User.email == "<EMAIL>"))
        admin_user = result.scalar_one_or_none()
        
        if not admin_user:
            print("✗ 未找到管理员用户，跳过模板创建")
            return
        
        # 演示模板
        templates = [
            {
                "name": "基础技术分析",
                "analysis_type": AnalysisType.TECHNICAL,
                "description": "包含常用技术指标的基础分析模板",
                "template_config": {
                    "indicators": ["SMA", "RSI", "MACD", "Bollinger Bands"],
                    "chart_types": ["candlestick", "line"],
                    "time_periods": [20, 50, 200]
                },
                "default_parameters": {
                    "period": 20,
                    "indicators": ["sma_20", "rsi", "macd"]
                },
                "is_public": True,
            },
            {
                "name": "基本面分析模板",
                "analysis_type": AnalysisType.FUNDAMENTAL,
                "description": "财务指标和估值分析模板",
                "template_config": {
                    "metrics": ["PE", "PB", "ROE", "Debt_to_Equity"],
                    "comparison_types": ["industry", "market"],
                    "time_range": "5_years"
                },
                "default_parameters": {
                    "years": 5,
                    "metrics": ["pe_ratio", "pb_ratio", "roe"]
                },
                "is_public": True,
            },
            {
                "name": "量化策略回测",
                "analysis_type": AnalysisType.QUANTITATIVE,
                "description": "量化交易策略回测模板",
                "template_config": {
                    "strategies": ["mean_reversion", "momentum", "pairs_trading"],
                    "risk_metrics": ["sharpe", "max_drawdown", "var"],
                    "backtest_period": "1_year"
                },
                "default_parameters": {
                    "strategy": "mean_reversion",
                    "backtest_period": 252,
                    "initial_capital": 100000
                },
                "is_public": True,
            },
        ]
        
        for template_data in templates:
            try:
                template = await analysis_service.create_template(
                    AnalysisTemplateCreate(**template_data),
                    admin_user.id
                )
                print(f"✓ 创建分析模板: {template.name}")
            except Exception as e:
                print(f"✗ 模板 {template_data['name']} 可能已存在: {e}")


async def main():
    """主函数"""
    print("开始初始化演示数据...")
    print(f"数据库URL: {settings.DATABASE_URL}")
    
    try:
        # 初始化数据库
        await init_db()
        print("✓ 数据库初始化完成")
        
        # 创建演示数据
        await create_demo_users()
        await create_demo_stocks()
        await create_demo_prices()
        await create_demo_templates()
        
        print("\n🎉 演示数据初始化完成！")
        print("\n可以使用以下账户登录:")
        print("管理员: <EMAIL> / Admin123456")
        print("普通用户: <EMAIL> / User123456")
        print("高级用户: <EMAIL> / Premium123456")
        
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())

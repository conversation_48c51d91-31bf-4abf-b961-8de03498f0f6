"""
用户认证相关的路由
"""
from datetime import timedelta
from typing import Any

from fastapi import APIRouter, Depends, status
from fastapi.security import HTTPAuthorizationCredentials

from src.auth.dependencies import get_current_active_user, security
from src.auth.models import User
from src.auth.schemas import (
    PasswordChange,
    Token,
    UserCreate,
    UserLogin,
    UserResponse,
    UserUpdate,
    RefreshToken,
)
from src.auth.service import AuthService, get_auth_service
from src.config import settings
from src.exceptions import AuthenticationException

router = APIRouter(prefix="/auth", tags=["认证"])


@router.post(
    "/register",
    response_model=UserResponse,
    status_code=status.HTTP_201_CREATED,
    summary="用户注册",
    description="创建新用户账户",
)
async def register(
    user_create: UserCreate,
    auth_service: AuthService = Depends(get_auth_service),
) -> Any:
    """用户注册"""
    user = await auth_service.create_user(user_create)
    return user


@router.post(
    "/login",
    response_model=Token,
    summary="用户登录",
    description="用户登录获取访问令牌",
)
async def login(
    user_login: UserLogin,
    auth_service: AuthService = Depends(get_auth_service),
) -> Any:
    """用户登录"""
    # 认证用户
    user = await auth_service.authenticate_user(user_login.email, user_login.password)
    
    # 创建令牌
    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    token_data = {
        "sub": str(user.id),
        "email": user.email,
        "role": user.role.value,
    }
    
    access_token = auth_service.create_access_token(
        data=token_data,
        expires_delta=access_token_expires
    )
    
    refresh_token = auth_service.create_refresh_token(data=token_data)
    
    return {
        "access_token": access_token,
        "refresh_token": refresh_token,
        "token_type": "bearer",
        "expires_in": settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
    }


@router.post(
    "/refresh",
    response_model=Token,
    summary="刷新令牌",
    description="使用刷新令牌获取新的访问令牌",
)
async def refresh_token(
    refresh_data: RefreshToken,
    auth_service: AuthService = Depends(get_auth_service),
) -> Any:
    """刷新令牌"""
    # 验证刷新令牌
    token_data = auth_service.verify_token(refresh_data.refresh_token)
    
    # 获取用户信息
    user = await auth_service.get_user_by_id(token_data.user_id)
    if not user or not user.is_active:
        raise AuthenticationException("User not found or inactive")
    
    # 创建新的令牌
    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    new_token_data = {
        "sub": str(user.id),
        "email": user.email,
        "role": user.role.value,
    }
    
    access_token = auth_service.create_access_token(
        data=new_token_data,
        expires_delta=access_token_expires
    )
    
    refresh_token = auth_service.create_refresh_token(data=new_token_data)
    
    return {
        "access_token": access_token,
        "refresh_token": refresh_token,
        "token_type": "bearer",
        "expires_in": settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
    }


@router.get(
    "/me",
    response_model=UserResponse,
    summary="获取当前用户信息",
    description="获取当前登录用户的详细信息",
)
async def get_current_user_info(
    current_user: User = Depends(get_current_active_user),
) -> Any:
    """获取当前用户信息"""
    return current_user


@router.put(
    "/me",
    response_model=UserResponse,
    summary="更新当前用户信息",
    description="更新当前登录用户的个人信息",
)
async def update_current_user(
    user_update: UserUpdate,
    current_user: User = Depends(get_current_active_user),
    auth_service: AuthService = Depends(get_auth_service),
) -> Any:
    """更新当前用户信息"""
    updated_user = await auth_service.update_user(current_user.id, user_update)
    return updated_user


@router.post(
    "/change-password",
    status_code=status.HTTP_200_OK,
    summary="修改密码",
    description="修改当前用户的密码",
)
async def change_password(
    password_change: PasswordChange,
    current_user: User = Depends(get_current_active_user),
    auth_service: AuthService = Depends(get_auth_service),
) -> Any:
    """修改密码"""
    await auth_service.change_password(
        current_user.id,
        password_change.current_password,
        password_change.new_password,
    )
    
    return {"message": "Password changed successfully"}


@router.post(
    "/logout",
    status_code=status.HTTP_200_OK,
    summary="用户登出",
    description="用户登出（客户端需要删除令牌）",
)
async def logout() -> Any:
    """用户登出"""
    # 在实际应用中，这里可以将令牌加入黑名单
    # 目前只是返回成功消息，客户端需要删除本地存储的令牌
    return {"message": "Logged out successfully"}

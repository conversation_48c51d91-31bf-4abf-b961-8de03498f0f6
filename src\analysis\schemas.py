"""
股票分析相关的Pydantic模型
"""
from datetime import datetime
from typing import Any, Dict, List, Optional
from uuid import UUID

from pydantic import BaseModel, Field

from src.constants import AnalysisType


class AnalysisBase(BaseModel):
    """分析基础模型"""
    
    title: str = Field(description="分析标题", max_length=200)
    symbol: str = Field(description="股票代码", max_length=20)
    analysis_type: AnalysisType = Field(description="分析类型")
    description: Optional[str] = Field(None, description="分析描述")


class AnalysisCreate(AnalysisBase):
    """创建分析模型"""
    
    parameters: Optional[Dict[str, Any]] = Field(None, description="分析参数")
    template_id: Optional[UUID] = Field(None, description="使用的模板ID")


class AnalysisUpdate(BaseModel):
    """更新分析模型"""
    
    title: Optional[str] = Field(None, description="分析标题", max_length=200)
    description: Optional[str] = Field(None, description="分析描述")
    parameters: Optional[Dict[str, Any]] = Field(None, description="分析参数")
    status: Optional[str] = Field(None, description="分析状态")


class AnalysisResponse(AnalysisBase):
    """分析响应模型"""
    
    id: UUID = Field(description="分析ID")
    user_id: UUID = Field(description="创建用户ID")
    content: Optional[Dict[str, Any]] = Field(None, description="分析内容")
    result: Optional[Dict[str, Any]] = Field(None, description="分析结果")
    parameters: Optional[Dict[str, Any]] = Field(None, description="分析参数")
    status: str = Field(description="分析状态")
    started_at: Optional[datetime] = Field(None, description="开始时间")
    completed_at: Optional[datetime] = Field(None, description="完成时间")
    error_message: Optional[str] = Field(None, description="错误信息")
    created_at: datetime = Field(description="创建时间")
    updated_at: datetime = Field(description="更新时间")
    
    class Config:
        from_attributes = True


class AnalysisResultBase(BaseModel):
    """分析结果基础模型"""
    
    result_type: str = Field(description="结果类型", max_length=50)
    title: str = Field(description="结果标题", max_length=200)
    data: Dict[str, Any] = Field(description="结果数据")
    config: Optional[Dict[str, Any]] = Field(None, description="结果配置")
    order_index: int = Field(default=0, description="排序索引")


class AnalysisResultCreate(AnalysisResultBase):
    """创建分析结果模型"""
    
    analysis_id: UUID = Field(description="分析ID")


class AnalysisResultResponse(AnalysisResultBase):
    """分析结果响应模型"""
    
    id: UUID = Field(description="结果ID")
    analysis_id: UUID = Field(description="分析ID")
    created_at: datetime = Field(description="创建时间")
    updated_at: datetime = Field(description="更新时间")
    
    class Config:
        from_attributes = True


class AnalysisTemplateBase(BaseModel):
    """分析模板基础模型"""
    
    name: str = Field(description="模板名称", max_length=100)
    analysis_type: AnalysisType = Field(description="分析类型")
    description: Optional[str] = Field(None, description="模板描述")
    template_config: Dict[str, Any] = Field(description="模板配置")
    default_parameters: Optional[Dict[str, Any]] = Field(None, description="默认参数")


class AnalysisTemplateCreate(AnalysisTemplateBase):
    """创建分析模板模型"""
    
    is_public: bool = Field(default=False, description="是否公开")


class AnalysisTemplateUpdate(BaseModel):
    """更新分析模板模型"""
    
    name: Optional[str] = Field(None, description="模板名称", max_length=100)
    description: Optional[str] = Field(None, description="模板描述")
    template_config: Optional[Dict[str, Any]] = Field(None, description="模板配置")
    default_parameters: Optional[Dict[str, Any]] = Field(None, description="默认参数")
    is_public: Optional[bool] = Field(None, description="是否公开")
    is_active: Optional[bool] = Field(None, description="是否激活")


class AnalysisTemplateResponse(AnalysisTemplateBase):
    """分析模板响应模型"""
    
    id: UUID = Field(description="模板ID")
    created_by: UUID = Field(description="创建者ID")
    is_public: bool = Field(description="是否公开")
    is_active: bool = Field(description="是否激活")
    created_at: datetime = Field(description="创建时间")
    updated_at: datetime = Field(description="更新时间")
    
    class Config:
        from_attributes = True


class TechnicalAnalysisParams(BaseModel):
    """技术分析参数"""
    
    indicators: List[str] = Field(description="技术指标列表")
    period: int = Field(default=20, description="计算周期", ge=1, le=200)
    start_date: Optional[str] = Field(None, description="开始日期 (YYYY-MM-DD)")
    end_date: Optional[str] = Field(None, description="结束日期 (YYYY-MM-DD)")


class FundamentalAnalysisParams(BaseModel):
    """基本面分析参数"""
    
    metrics: List[str] = Field(description="财务指标列表")
    years: int = Field(default=5, description="分析年数", ge=1, le=10)
    comparison_symbols: Optional[List[str]] = Field(None, description="对比股票代码")


class QuantitativeAnalysisParams(BaseModel):
    """量化分析参数"""
    
    strategy: str = Field(description="策略名称")
    backtest_period: int = Field(default=252, description="回测周期（天）", ge=30, le=1000)
    initial_capital: float = Field(default=100000, description="初始资金", gt=0)
    parameters: Dict[str, Any] = Field(description="策略参数")


class AnalysisQuery(BaseModel):
    """分析查询参数"""
    
    symbol: Optional[str] = Field(None, description="股票代码")
    analysis_type: Optional[AnalysisType] = Field(None, description="分析类型")
    status: Optional[str] = Field(None, description="分析状态")
    user_id: Optional[UUID] = Field(None, description="用户ID")


class AnalysisExecuteRequest(BaseModel):
    """执行分析请求"""
    
    analysis_id: UUID = Field(description="分析ID")
    force_refresh: bool = Field(default=False, description="是否强制刷新")


class AnalysisMetrics(BaseModel):
    """分析指标"""
    
    total_analyses: int = Field(description="总分析数")
    completed_analyses: int = Field(description="已完成分析数")
    failed_analyses: int = Field(description="失败分析数")
    avg_execution_time: Optional[float] = Field(None, description="平均执行时间（秒）")
    popular_symbols: List[str] = Field(description="热门股票代码")
    popular_types: List[str] = Field(description="热门分析类型")

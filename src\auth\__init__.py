"""
用户认证模块
"""
from src.auth.dependencies import (
    get_current_active_user,
    get_current_user,
    get_current_verified_user,
    get_optional_current_user,
    require_admin,
    require_premium,
    require_role,
    require_roles,
    validate_user_access,
)
from src.auth.models import User
from src.auth.router import router
from src.auth.schemas import (
    PasswordChange,
    Token,
    TokenData,
    UserCreate,
    UserLogin,
    UserResponse,
    UserUpdate,
)
from src.auth.service import AuthService, get_auth_service

__all__ = [
    # Models
    "User",
    # Schemas
    "UserCreate",
    "UserLogin",
    "UserResponse",
    "UserUpdate",
    "Token",
    "TokenData",
    "PasswordChange",
    # Service
    "AuthService",
    "get_auth_service",
    # Dependencies
    "get_current_user",
    "get_current_active_user",
    "get_current_verified_user",
    "get_optional_current_user",
    "require_role",
    "require_roles",
    "require_admin",
    "require_premium",
    "validate_user_access",
    # Router
    "router",
]

events {
    worker_connections 1024;
}

http {
    upstream app {
        server app:8000;
    }

    server {
        listen 80;
        server_name localhost;

        # 客户端最大请求体大小
        client_max_body_size 10M;

        # 代理设置
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # API 路由
        location /api/ {
            proxy_pass http://app;
            proxy_timeout 300s;
            proxy_read_timeout 300s;
            proxy_connect_timeout 300s;
        }

        # 文档路由
        location /docs {
            proxy_pass http://app;
        }

        location /redoc {
            proxy_pass http://app;
        }

        location /openapi.json {
            proxy_pass http://app;
        }

        # 健康检查
        location /health {
            proxy_pass http://app;
        }

        # 根路径
        location / {
            proxy_pass http://app;
        }

        # 静态文件缓存
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
            proxy_pass http://app;
        }
    }
}

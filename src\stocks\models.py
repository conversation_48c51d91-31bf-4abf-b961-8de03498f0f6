"""
股票数据相关的数据库模型
"""
from datetime import date, datetime
from decimal import Decimal
from typing import Optional

from sqlalchemy import Date, DateTime, Numeric, String, Text, Index
from sqlalchemy.orm import Mapped, mapped_column

from src.constants import StockMarket
from src.models import BaseModel


class Stock(BaseModel):
    """股票基本信息模型"""
    
    __tablename__ = "stocks"
    
    # 基本信息
    symbol: Mapped[str] = mapped_column(
        String(20),
        unique=True,
        index=True,
        nullable=False,
        comment="股票代码"
    )
    
    name: Mapped[str] = mapped_column(
        String(100),
        nullable=False,
        comment="股票名称"
    )
    
    market: Mapped[StockMarket] = mapped_column(
        String(20),
        nullable=False,
        comment="交易市场"
    )
    
    # 公司信息
    company_name: Mapped[Optional[str]] = mapped_column(
        String(200),
        nullable=True,
        comment="公司全称"
    )
    
    industry: Mapped[Optional[str]] = mapped_column(
        String(100),
        nullable=True,
        comment="所属行业"
    )
    
    sector: Mapped[Optional[str]] = mapped_column(
        String(100),
        nullable=True,
        comment="所属板块"
    )
    
    description: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        comment="公司描述"
    )
    
    # 上市信息
    list_date: Mapped[Optional[date]] = mapped_column(
        Date,
        nullable=True,
        comment="上市日期"
    )
    
    # 基本财务指标
    market_cap: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(20, 2),
        nullable=True,
        comment="市值"
    )
    
    total_shares: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(20, 2),
        nullable=True,
        comment="总股本"
    )
    
    float_shares: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(20, 2),
        nullable=True,
        comment="流通股本"
    )
    
    # 状态信息
    is_active: Mapped[bool] = mapped_column(
        default=True,
        nullable=False,
        comment="是否活跃交易"
    )
    
    def __repr__(self) -> str:
        return f"<Stock(symbol={self.symbol}, name={self.name}, market={self.market})>"


class StockPrice(BaseModel):
    """股票价格数据模型"""
    
    __tablename__ = "stock_prices"
    
    # 股票信息
    symbol: Mapped[str] = mapped_column(
        String(20),
        nullable=False,
        index=True,
        comment="股票代码"
    )
    
    # 时间信息
    trade_date: Mapped[date] = mapped_column(
        Date,
        nullable=False,
        index=True,
        comment="交易日期"
    )
    
    trade_time: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        comment="交易时间"
    )
    
    # 价格信息
    open_price: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(10, 4),
        nullable=True,
        comment="开盘价"
    )
    
    high_price: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(10, 4),
        nullable=True,
        comment="最高价"
    )
    
    low_price: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(10, 4),
        nullable=True,
        comment="最低价"
    )
    
    close_price: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(10, 4),
        nullable=True,
        comment="收盘价"
    )
    
    # 成交信息
    volume: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(20, 2),
        nullable=True,
        comment="成交量"
    )
    
    amount: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(20, 2),
        nullable=True,
        comment="成交额"
    )
    
    # 涨跌信息
    change: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(10, 4),
        nullable=True,
        comment="涨跌额"
    )
    
    change_pct: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(8, 4),
        nullable=True,
        comment="涨跌幅(%)"
    )
    
    # 技术指标
    turnover_rate: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(8, 4),
        nullable=True,
        comment="换手率(%)"
    )
    
    pe_ratio: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(10, 4),
        nullable=True,
        comment="市盈率"
    )
    
    pb_ratio: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(10, 4),
        nullable=True,
        comment="市净率"
    )
    
    # 复合索引
    __table_args__ = (
        Index("idx_symbol_date", "symbol", "trade_date"),
        Index("idx_date_symbol", "trade_date", "symbol"),
    )
    
    def __repr__(self) -> str:
        return f"<StockPrice(symbol={self.symbol}, date={self.trade_date}, close={self.close_price})>"


class StockDividend(BaseModel):
    """股票分红数据模型"""
    
    __tablename__ = "stock_dividends"
    
    # 股票信息
    symbol: Mapped[str] = mapped_column(
        String(20),
        nullable=False,
        index=True,
        comment="股票代码"
    )
    
    # 分红信息
    ex_date: Mapped[date] = mapped_column(
        Date,
        nullable=False,
        comment="除权除息日"
    )
    
    record_date: Mapped[Optional[date]] = mapped_column(
        Date,
        nullable=True,
        comment="股权登记日"
    )
    
    pay_date: Mapped[Optional[date]] = mapped_column(
        Date,
        nullable=True,
        comment="派息日"
    )
    
    # 分红金额
    cash_dividend: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(10, 4),
        nullable=True,
        comment="现金分红(每股)"
    )
    
    stock_dividend: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(10, 4),
        nullable=True,
        comment="股票分红(每股)"
    )
    
    # 复合索引
    __table_args__ = (
        Index("idx_symbol_ex_date", "symbol", "ex_date"),
    )
    
    def __repr__(self) -> str:
        return f"<StockDividend(symbol={self.symbol}, ex_date={self.ex_date})>"

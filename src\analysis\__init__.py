"""
股票分析模块
"""
from src.analysis.dependencies import (
    get_analysis_by_id,
    get_analysis_query_params,
    get_template_by_id,
    get_template_query_params,
    get_user_analysis,
    get_user_template,
)
from src.analysis.models import Analysis, AnalysisResult, AnalysisTemplate
from src.analysis.router import router
from src.analysis.schemas import (
    AnalysisCreate,
    AnalysisQuery,
    AnalysisResponse,
    AnalysisResultCreate,
    AnalysisResultResponse,
    AnalysisTemplateCreate,
    AnalysisTemplateResponse,
    AnalysisTemplateUpdate,
    AnalysisUpdate,
    FundamentalAnalysisParams,
    QuantitativeAnalysisParams,
    TechnicalAnalysisParams,
)
from src.analysis.service import AnalysisService, get_analysis_service

__all__ = [
    # Models
    "Analysis",
    "AnalysisResult",
    "AnalysisTemplate",
    # Schemas
    "AnalysisCreate",
    "AnalysisUpdate",
    "AnalysisResponse",
    "AnalysisQuery",
    "AnalysisResultCreate",
    "AnalysisResultResponse",
    "AnalysisTemplateCreate",
    "AnalysisTemplateUpdate",
    "AnalysisTemplateResponse",
    "TechnicalAnalysisParams",
    "FundamentalAnalysisParams",
    "QuantitativeAnalysisParams",
    # Service
    "AnalysisService",
    "get_analysis_service",
    # Dependencies
    "get_analysis_by_id",
    "get_user_analysis",
    "get_template_by_id",
    "get_user_template",
    "get_analysis_query_params",
    "get_template_query_params",
    # Router
    "router",
]

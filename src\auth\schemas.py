"""
用户认证相关的Pydantic模型
"""
from datetime import datetime
from typing import Optional
from uuid import UUID

from pydantic import BaseModel, EmailStr, Field, field_validator

from src.constants import UserRole


class UserBase(BaseModel):
    """用户基础模型"""
    
    email: EmailStr = Field(description="邮箱地址")
    username: str = Field(min_length=3, max_length=50, description="用户名")
    full_name: Optional[str] = Field(None, max_length=100, description="全名")
    
    @field_validator("username")
    @classmethod
    def validate_username(cls, v: str) -> str:
        """验证用户名格式"""
        if not v.replace("_", "").replace("-", "").isalnum():
            raise ValueError("用户名只能包含字母、数字、下划线和连字符")
        return v


class UserCreate(UserBase):
    """创建用户模型"""
    
    password: str = Field(min_length=8, max_length=100, description="密码")
    
    @field_validator("password")
    @classmethod
    def validate_password(cls, v: str) -> str:
        """验证密码强度"""
        if not any(c.isupper() for c in v):
            raise ValueError("密码必须包含至少一个大写字母")
        if not any(c.islower() for c in v):
            raise ValueError("密码必须包含至少一个小写字母")
        if not any(c.isdigit() for c in v):
            raise ValueError("密码必须包含至少一个数字")
        return v


class UserUpdate(BaseModel):
    """更新用户模型"""
    
    full_name: Optional[str] = Field(None, max_length=100, description="全名")
    bio: Optional[str] = Field(None, max_length=500, description="个人简介")
    avatar_url: Optional[str] = Field(None, max_length=500, description="头像URL")
    timezone: Optional[str] = Field(None, max_length=50, description="时区")
    language: Optional[str] = Field(None, max_length=10, description="语言偏好")


class UserResponse(UserBase):
    """用户响应模型"""
    
    id: UUID = Field(description="用户ID")
    role: UserRole = Field(description="用户角色")
    is_active: bool = Field(description="是否激活")
    is_verified: bool = Field(description="是否已验证邮箱")
    created_at: datetime = Field(description="创建时间")
    updated_at: datetime = Field(description="更新时间")
    last_login_at: Optional[datetime] = Field(None, description="最后登录时间")
    avatar_url: Optional[str] = Field(None, description="头像URL")
    bio: Optional[str] = Field(None, description="个人简介")
    timezone: str = Field(description="时区")
    language: str = Field(description="语言偏好")
    
    class Config:
        from_attributes = True


class UserLogin(BaseModel):
    """用户登录模型"""
    
    email: EmailStr = Field(description="邮箱地址")
    password: str = Field(min_length=1, description="密码")


class Token(BaseModel):
    """令牌模型"""
    
    access_token: str = Field(description="访问令牌")
    refresh_token: str = Field(description="刷新令牌")
    token_type: str = Field(default="bearer", description="令牌类型")
    expires_in: int = Field(description="过期时间（秒）")


class TokenData(BaseModel):
    """令牌数据模型"""
    
    user_id: UUID = Field(description="用户ID")
    email: str = Field(description="邮箱地址")
    role: UserRole = Field(description="用户角色")


class RefreshToken(BaseModel):
    """刷新令牌模型"""
    
    refresh_token: str = Field(description="刷新令牌")


class PasswordChange(BaseModel):
    """修改密码模型"""
    
    current_password: str = Field(min_length=1, description="当前密码")
    new_password: str = Field(min_length=8, max_length=100, description="新密码")
    
    @field_validator("new_password")
    @classmethod
    def validate_new_password(cls, v: str) -> str:
        """验证新密码强度"""
        if not any(c.isupper() for c in v):
            raise ValueError("密码必须包含至少一个大写字母")
        if not any(c.islower() for c in v):
            raise ValueError("密码必须包含至少一个小写字母")
        if not any(c.isdigit() for c in v):
            raise ValueError("密码必须包含至少一个数字")
        return v


class PasswordReset(BaseModel):
    """重置密码模型"""
    
    email: EmailStr = Field(description="邮箱地址")


class PasswordResetConfirm(BaseModel):
    """确认重置密码模型"""
    
    token: str = Field(description="重置令牌")
    new_password: str = Field(min_length=8, max_length=100, description="新密码")
    
    @field_validator("new_password")
    @classmethod
    def validate_new_password(cls, v: str) -> str:
        """验证新密码强度"""
        if not any(c.isupper() for c in v):
            raise ValueError("密码必须包含至少一个大写字母")
        if not any(c.islower() for c in v):
            raise ValueError("密码必须包含至少一个小写字母")
        if not any(c.isdigit() for c in v):
            raise ValueError("密码必须包含至少一个数字")
        return v

"""
开发环境启动脚本
"""
import os
import sys
import subprocess
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def check_requirements():
    """检查依赖是否安装"""
    print("检查依赖...")
    
    required_packages = [
        "fastapi",
        "uvicorn",
        "sqlalchemy",
        "asyncpg",
        "pydantic",
        "python-jose",
        "passlib",
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace("-", "_"))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ 缺少以下依赖包: {', '.join(missing_packages)}")
        print("请运行: pip install -r requirements/dev.txt")
        return False
    
    print("✓ 依赖检查通过")
    return True


def check_env_file():
    """检查环境变量文件"""
    env_file = project_root / ".env"
    env_example = project_root / ".env.example"
    
    if not env_file.exists():
        if env_example.exists():
            print("❌ 未找到 .env 文件")
            print("请复制 .env.example 为 .env 并配置相应的环境变量")
            print("命令: cp .env.example .env")
        else:
            print("❌ 未找到 .env 和 .env.example 文件")
        return False
    
    print("✓ 环境变量文件检查通过")
    return True


def check_database():
    """检查数据库连接"""
    print("检查数据库连接...")
    
    try:
        from src.config import settings
        print(f"数据库URL: {settings.DATABASE_URL}")
        
        # 这里可以添加实际的数据库连接测试
        # 为了简化，暂时跳过实际连接测试
        print("✓ 数据库配置检查通过")
        return True
        
    except Exception as e:
        print(f"❌ 数据库配置错误: {e}")
        return False


def run_migrations():
    """运行数据库迁移"""
    print("检查数据库迁移...")
    
    try:
        # 检查是否有迁移文件
        alembic_versions = project_root / "alembic" / "versions"
        if not alembic_versions.exists() or not list(alembic_versions.glob("*.py")):
            print("创建初始迁移...")
            subprocess.run([
                sys.executable, "-m", "alembic", "revision", 
                "--autogenerate", "-m", "Initial migration"
            ], cwd=project_root, check=True)
        
        # 执行迁移
        print("执行数据库迁移...")
        subprocess.run([
            sys.executable, "-m", "alembic", "upgrade", "head"
        ], cwd=project_root, check=True)
        
        print("✓ 数据库迁移完成")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 数据库迁移失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 迁移过程出错: {e}")
        return False


def init_demo_data():
    """初始化演示数据"""
    print("是否初始化演示数据? (y/N): ", end="")
    response = input().strip().lower()
    
    if response in ['y', 'yes']:
        try:
            print("初始化演示数据...")
            subprocess.run([
                sys.executable, "scripts/init_demo_data.py"
            ], cwd=project_root, check=True)
            print("✓ 演示数据初始化完成")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ 演示数据初始化失败: {e}")
            return False
    else:
        print("跳过演示数据初始化")
        return True


def start_server():
    """启动开发服务器"""
    print("启动开发服务器...")
    
    try:
        # 使用 uvicorn 启动服务器
        subprocess.run([
            sys.executable, "-m", "uvicorn",
            "src.main:app",
            "--reload",
            "--host", "0.0.0.0",
            "--port", "8000",
            "--log-level", "info"
        ], cwd=project_root)
        
    except KeyboardInterrupt:
        print("\n服务器已停止")
    except Exception as e:
        print(f"❌ 启动服务器失败: {e}")


def main():
    """主函数"""
    print("🚀 股票分析系统开发环境启动")
    print("=" * 50)
    
    # 检查各项配置
    checks = [
        check_requirements,
        check_env_file,
        check_database,
        run_migrations,
        init_demo_data,
    ]
    
    for check in checks:
        if not check():
            print("\n❌ 启动失败，请解决上述问题后重试")
            return
        print()
    
    print("✅ 所有检查通过，启动服务器...")
    print("API 文档地址: http://localhost:8000/docs")
    print("按 Ctrl+C 停止服务器")
    print("=" * 50)
    
    start_server()


if __name__ == "__main__":
    main()

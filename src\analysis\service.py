"""
股票分析服务
"""
import asyncio
from datetime import datetime
from typing import Any, Dict, List, Optional, Sequence
from uuid import UUID

from fastapi import Depends
from sqlalchemy import and_, desc, func, select
from sqlalchemy.ext.asyncio import AsyncSession

from src.analysis.models import Analysis, AnalysisResult, AnalysisTemplate
from src.analysis.schemas import (
    AnalysisCreate,
    AnalysisQuery,
    AnalysisResultCreate,
    AnalysisTemplateCreate,
    AnalysisTemplateUpdate,
    AnalysisUpdate,
)
from src.constants import AnalysisType
from src.database import get_db
from src.exceptions import AnalysisFailedException, NotFoundException
from src.pagination import PaginationParams, paginate


class AnalysisService:
    """分析服务类"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def get_analysis_by_id(self, analysis_id: UUID) -> Optional[Analysis]:
        """根据ID获取分析"""
        query = select(Analysis).where(Analysis.id == analysis_id)
        result = await self.db.execute(query)
        return result.scalar_one_or_none()
    
    async def create_analysis(
        self,
        analysis_create: AnalysisCreate,
        user_id: UUID,
    ) -> Analysis:
        """创建分析"""
        # 如果指定了模板，获取模板配置
        template_config = None
        if analysis_create.template_id:
            template = await self.get_template_by_id(analysis_create.template_id)
            if template:
                template_config = template.template_config
                # 合并默认参数
                if template.default_parameters:
                    parameters = template.default_parameters.copy()
                    if analysis_create.parameters:
                        parameters.update(analysis_create.parameters)
                    analysis_create.parameters = parameters
        
        # 创建分析记录
        db_analysis = Analysis(
            title=analysis_create.title,
            symbol=analysis_create.symbol.upper(),
            analysis_type=analysis_create.analysis_type,
            description=analysis_create.description,
            user_id=user_id,
            parameters=analysis_create.parameters,
            content=template_config,
            status="pending",
        )
        
        self.db.add(db_analysis)
        await self.db.commit()
        await self.db.refresh(db_analysis)
        
        return db_analysis
    
    async def update_analysis(
        self,
        analysis_id: UUID,
        analysis_update: AnalysisUpdate,
    ) -> Analysis:
        """更新分析"""
        analysis = await self.get_analysis_by_id(analysis_id)
        if not analysis:
            raise NotFoundException("Analysis not found")
        
        # 更新字段
        for field, value in analysis_update.model_dump(exclude_unset=True).items():
            setattr(analysis, field, value)
        
        await self.db.commit()
        await self.db.refresh(analysis)
        
        return analysis
    
    async def delete_analysis(self, analysis_id: UUID) -> bool:
        """删除分析"""
        analysis = await self.get_analysis_by_id(analysis_id)
        if not analysis:
            raise NotFoundException("Analysis not found")
        
        await self.db.delete(analysis)
        await self.db.commit()
        
        return True
    
    async def search_analyses(
        self,
        query_params: AnalysisQuery,
        pagination: PaginationParams,
        user_id: Optional[UUID] = None,
    ) -> tuple[Sequence[Analysis], int]:
        """搜索分析"""
        query = select(Analysis)
        
        # 构建查询条件
        conditions = []
        
        if user_id:
            conditions.append(Analysis.user_id == user_id)
        
        if query_params.symbol:
            conditions.append(Analysis.symbol.ilike(f"%{query_params.symbol}%"))
        
        if query_params.analysis_type:
            conditions.append(Analysis.analysis_type == query_params.analysis_type)
        
        if query_params.status:
            conditions.append(Analysis.status == query_params.status)
        
        if query_params.user_id:
            conditions.append(Analysis.user_id == query_params.user_id)
        
        if conditions:
            query = query.where(and_(*conditions))
        
        # 排序
        query = query.order_by(desc(Analysis.created_at))
        
        # 分页查询
        return await paginate(self.db, query, pagination)
    
    async def execute_analysis(self, analysis_id: UUID) -> Analysis:
        """执行分析"""
        analysis = await self.get_analysis_by_id(analysis_id)
        if not analysis:
            raise NotFoundException("Analysis not found")
        
        # 更新状态为运行中
        analysis.status = "running"
        analysis.started_at = datetime.utcnow()
        await self.db.commit()
        
        try:
            # 根据分析类型执行不同的分析逻辑
            if analysis.analysis_type == AnalysisType.TECHNICAL:
                result = await self._execute_technical_analysis(analysis)
            elif analysis.analysis_type == AnalysisType.FUNDAMENTAL:
                result = await self._execute_fundamental_analysis(analysis)
            elif analysis.analysis_type == AnalysisType.QUANTITATIVE:
                result = await self._execute_quantitative_analysis(analysis)
            elif analysis.analysis_type == AnalysisType.SENTIMENT:
                result = await self._execute_sentiment_analysis(analysis)
            else:
                raise AnalysisFailedException(f"Unsupported analysis type: {analysis.analysis_type}")
            
            # 更新分析结果
            analysis.result = result
            analysis.status = "completed"
            analysis.completed_at = datetime.utcnow()
            analysis.error_message = None
            
        except Exception as e:
            # 更新错误状态
            analysis.status = "failed"
            analysis.completed_at = datetime.utcnow()
            analysis.error_message = str(e)
            
            await self.db.commit()
            raise AnalysisFailedException(f"Analysis execution failed: {str(e)}")
        
        await self.db.commit()
        await self.db.refresh(analysis)
        
        return analysis
    
    async def _execute_technical_analysis(self, analysis: Analysis) -> Dict[str, Any]:
        """执行技术分析"""
        # 这里是技术分析的示例实现
        # 在实际应用中，这里会调用具体的技术分析算法
        
        # 模拟分析过程
        await asyncio.sleep(1)  # 模拟计算时间
        
        # 返回示例结果
        return {
            "indicators": {
                "sma_20": 150.25,
                "sma_50": 148.75,
                "rsi": 65.4,
                "macd": 2.15,
            },
            "signals": [
                {"type": "buy", "strength": 0.7, "reason": "RSI oversold"},
                {"type": "hold", "strength": 0.5, "reason": "MACD neutral"},
            ],
            "summary": "技术指标显示股票处于中性偏多状态",
        }
    
    async def _execute_fundamental_analysis(self, analysis: Analysis) -> Dict[str, Any]:
        """执行基本面分析"""
        # 模拟基本面分析
        await asyncio.sleep(2)
        
        return {
            "financial_ratios": {
                "pe_ratio": 18.5,
                "pb_ratio": 2.3,
                "roe": 0.15,
                "debt_to_equity": 0.4,
            },
            "growth_metrics": {
                "revenue_growth": 0.12,
                "earnings_growth": 0.08,
                "dividend_yield": 0.025,
            },
            "valuation": "undervalued",
            "summary": "公司基本面良好，估值合理",
        }
    
    async def _execute_quantitative_analysis(self, analysis: Analysis) -> Dict[str, Any]:
        """执行量化分析"""
        # 模拟量化分析
        await asyncio.sleep(3)
        
        return {
            "backtest_results": {
                "total_return": 0.25,
                "annual_return": 0.12,
                "sharpe_ratio": 1.8,
                "max_drawdown": -0.08,
                "win_rate": 0.65,
            },
            "risk_metrics": {
                "volatility": 0.18,
                "beta": 1.2,
                "var_95": -0.03,
            },
            "summary": "策略表现良好，风险可控",
        }
    
    async def _execute_sentiment_analysis(self, analysis: Analysis) -> Dict[str, Any]:
        """执行情绪分析"""
        # 模拟情绪分析
        await asyncio.sleep(1.5)
        
        return {
            "sentiment_score": 0.65,
            "sentiment_label": "positive",
            "news_sentiment": {
                "positive": 12,
                "neutral": 8,
                "negative": 3,
            },
            "social_sentiment": {
                "mentions": 156,
                "positive_ratio": 0.68,
                "trending": True,
            },
            "summary": "市场情绪偏向积极",
        }
    
    # 模板相关方法
    async def get_template_by_id(self, template_id: UUID) -> Optional[AnalysisTemplate]:
        """根据ID获取模板"""
        query = select(AnalysisTemplate).where(AnalysisTemplate.id == template_id)
        result = await self.db.execute(query)
        return result.scalar_one_or_none()
    
    async def create_template(
        self,
        template_create: AnalysisTemplateCreate,
        user_id: UUID,
    ) -> AnalysisTemplate:
        """创建分析模板"""
        db_template = AnalysisTemplate(
            **template_create.model_dump(),
            created_by=user_id,
        )
        
        self.db.add(db_template)
        await self.db.commit()
        await self.db.refresh(db_template)
        
        return db_template
    
    async def get_templates(
        self,
        analysis_type: Optional[AnalysisType] = None,
        user_id: Optional[UUID] = None,
        public_only: bool = False,
    ) -> Sequence[AnalysisTemplate]:
        """获取模板列表"""
        query = select(AnalysisTemplate).where(AnalysisTemplate.is_active == True)
        
        if analysis_type:
            query = query.where(AnalysisTemplate.analysis_type == analysis_type)
        
        if public_only:
            query = query.where(AnalysisTemplate.is_public == True)
        elif user_id:
            # 获取公开模板或用户自己的模板
            query = query.where(
                (AnalysisTemplate.is_public == True) |
                (AnalysisTemplate.created_by == user_id)
            )
        
        query = query.order_by(AnalysisTemplate.name)
        
        result = await self.db.execute(query)
        return result.scalars().all()


def get_analysis_service(db: AsyncSession = Depends(get_db)) -> AnalysisService:
    """获取分析服务实例"""
    return AnalysisService(db)

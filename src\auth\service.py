"""
用户认证服务
"""
from datetime import datetime, timedelta
from typing import Optional
from uuid import UUID

from fastapi import Depends
from jose import JWTError, jwt
from passlib.context import CryptContext
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from src.auth.models import User
from src.auth.schemas import TokenData, UserCreate, UserUpdate
from src.config import settings
from src.database import get_db
from src.exceptions import (
    AuthenticationException,
    InvalidCredentialsException,
    TokenExpiredException,
    UserAlreadyExistsException,
    UserNotFoundException,
)

# 密码加密上下文
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


class AuthService:
    """认证服务类"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    @staticmethod
    def verify_password(plain_password: str, hashed_password: str) -> bool:
        """验证密码"""
        return pwd_context.verify(plain_password, hashed_password)
    
    @staticmethod
    def get_password_hash(password: str) -> str:
        """获取密码哈希"""
        return pwd_context.hash(password)
    
    @staticmethod
    def create_access_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
        """创建访问令牌"""
        to_encode = data.copy()
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        
        to_encode.update({"exp": expire})
        encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
        return encoded_jwt
    
    @staticmethod
    def create_refresh_token(data: dict) -> str:
        """创建刷新令牌"""
        to_encode = data.copy()
        expire = datetime.utcnow() + timedelta(days=settings.REFRESH_TOKEN_EXPIRE_DAYS)
        to_encode.update({"exp": expire})
        encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
        return encoded_jwt
    
    @staticmethod
    def verify_token(token: str) -> TokenData:
        """验证令牌"""
        try:
            payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
            user_id: str = payload.get("sub")
            email: str = payload.get("email")
            role: str = payload.get("role")
            
            if user_id is None or email is None:
                raise AuthenticationException("Invalid token")
            
            return TokenData(user_id=UUID(user_id), email=email, role=role)
        except JWTError:
            raise TokenExpiredException()
    
    async def get_user_by_email(self, email: str) -> Optional[User]:
        """根据邮箱获取用户"""
        query = select(User).where(User.email == email)
        result = await self.db.execute(query)
        return result.scalar_one_or_none()
    
    async def get_user_by_id(self, user_id: UUID) -> Optional[User]:
        """根据ID获取用户"""
        query = select(User).where(User.id == user_id)
        result = await self.db.execute(query)
        return result.scalar_one_or_none()
    
    async def get_user_by_username(self, username: str) -> Optional[User]:
        """根据用户名获取用户"""
        query = select(User).where(User.username == username)
        result = await self.db.execute(query)
        return result.scalar_one_or_none()
    
    async def create_user(self, user_create: UserCreate) -> User:
        """创建用户"""
        # 检查邮箱是否已存在
        existing_user = await self.get_user_by_email(user_create.email)
        if existing_user:
            raise UserAlreadyExistsException(user_create.email)
        
        # 检查用户名是否已存在
        existing_username = await self.get_user_by_username(user_create.username)
        if existing_username:
            raise UserAlreadyExistsException(f"Username {user_create.username} already exists")
        
        # 创建新用户
        hashed_password = self.get_password_hash(user_create.password)
        db_user = User(
            email=user_create.email,
            username=user_create.username,
            full_name=user_create.full_name,
            hashed_password=hashed_password,
        )
        
        self.db.add(db_user)
        await self.db.commit()
        await self.db.refresh(db_user)
        
        return db_user
    
    async def authenticate_user(self, email: str, password: str) -> User:
        """认证用户"""
        user = await self.get_user_by_email(email)
        if not user:
            raise InvalidCredentialsException()
        
        if not self.verify_password(password, user.hashed_password):
            raise InvalidCredentialsException()
        
        if not user.is_active:
            raise AuthenticationException("User account is inactive")
        
        # 更新最后登录时间
        user.last_login_at = datetime.utcnow()
        await self.db.commit()
        
        return user
    
    async def update_user(self, user_id: UUID, user_update: UserUpdate) -> User:
        """更新用户信息"""
        user = await self.get_user_by_id(user_id)
        if not user:
            raise UserNotFoundException(str(user_id))
        
        # 更新字段
        for field, value in user_update.model_dump(exclude_unset=True).items():
            setattr(user, field, value)
        
        await self.db.commit()
        await self.db.refresh(user)
        
        return user
    
    async def change_password(self, user_id: UUID, current_password: str, new_password: str) -> bool:
        """修改密码"""
        user = await self.get_user_by_id(user_id)
        if not user:
            raise UserNotFoundException(str(user_id))
        
        if not self.verify_password(current_password, user.hashed_password):
            raise InvalidCredentialsException()
        
        user.hashed_password = self.get_password_hash(new_password)
        await self.db.commit()
        
        return True


def get_auth_service(db: AsyncSession = Depends(get_db)) -> AuthService:
    """获取认证服务实例"""
    return AuthService(db)
